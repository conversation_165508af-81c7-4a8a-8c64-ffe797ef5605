/**
 * ES Module Chat Flow Test Script
 * 
 * This script tests the chat flow between admin and supplier accounts.
 * Run with: node scripts/esm-chat-test.js
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import chalk from 'chalk';

// Load environment variables
dotenv.config();

// Supabase configuration
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://qcnotlojmyvpqbbgoxbc.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseKey) {
  console.error(chalk.red('Error: Supabase key not found. Please set VITE_SUPABASE_ANON_KEY in your .env file.'));
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Configuration
const config = {
  adminEmail: '<EMAIL>',
  adminPassword: process.env.ADMIN_PASSWORD || 'test123', // Set this in your .env file
  supplierEmail: '<EMAIL>', // Use an existing supplier account
  taskId: null,
  adminId: null,
  supplierId: null,
  threadId: null
};

// Utility functions
const log = (message) => console.log(chalk.green(`✓ ${message}`));
const info = (message) => console.log(chalk.blue(`ℹ ${message}`));
const error = (message) => console.error(chalk.red(`✗ ${message}`));
const warn = (message) => console.warn(chalk.yellow(`⚠ ${message}`));
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Login as admin
async function loginAsAdmin() {
  info('Logging in as admin...');
  
  if (!config.adminPassword) {
    error('Admin password not set. Please set ADMIN_PASSWORD in your .env file.');
    process.exit(1);
  }
  
  const { data, error: loginError } = await supabase.auth.signInWithPassword({
    email: config.adminEmail,
    password: config.adminPassword
  });
  
  if (loginError) {
    error(`Login failed: ${loginError.message}`);
    process.exit(1);
  }
  
  config.adminId = data.user.id;
  log(`Logged in as admin (${config.adminId})`);
  return data.user;
}

// Create a test task
async function createTestTask() {
  info('Creating test task...');
  
  const { data: task, error: taskError } = await supabase
    .from('tasks')
    .insert({
      title: `Chat Test Task ${new Date().toISOString().substring(0, 16)}`,
      description: 'This is a test task to verify the chat flow functionality.',
      category: 'Maintenance',
      budget: 100,
      due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      location: 'Test Location',
      visibility: 'public',
      status: 'open',
      user_id: config.adminId
    })
    .select()
    .single();
  
  if (taskError) {
    error(`Failed to create task: ${taskError.message}`);
    return null;
  }
  
  config.taskId = task.id;
  log(`Task created with ID: ${task.id}`);
  
  // Create initial system message
  const { error: msgError } = await supabase
    .from('task_messages')
    .insert({
      task_id: config.taskId,
      sender_id: '********-0000-0000-0000-************', // System user
      content: 'Task created and is now open for offers.'
    });
  
  if (msgError) {
    warn(`Failed to create initial system message: ${msgError.message}`);
  }
  
  return task;
}

// Find supplier account
async function findSupplier() {
  info('Finding supplier account...');
  
  const { data: suppliers, error: supplierError } = await supabase
    .from('profiles')
    .select('id, email, role')
    .eq('role', 'supplier')
    .limit(5);
  
  if (supplierError) {
    error(`Failed to find suppliers: ${supplierError.message}`);
    return null;
  }
  
  if (suppliers.length === 0) {
    error('No supplier accounts found.');
    return null;
  }
  
  // Find the specific supplier or use the first one
  const supplier = suppliers.find(s => 
    s.email && s.email[0] && s.email[0].includes(config.supplierEmail)
  ) || suppliers[0];
  
  config.supplierId = supplier.id;
  config.supplierEmail = supplier.email[0];
  
  log(`Using supplier: ${config.supplierEmail} (ID: ${config.supplierId})`);
  return supplier;
}

// Simulate supplier expressing interest
async function simulateExpressInterest() {
  info('Simulating supplier expressing interest...');
  
  // Create chat thread
  const { data: thread, error: threadError } = await supabase
    .from('chat_threads')
    .insert({
      task_id: config.taskId,
      supplier_id: config.supplierId,
      admin_id: config.adminId,
      status: 'interest',
      has_offer: false
    })
    .select()
    .single();
  
  if (threadError) {
    error(`Failed to create chat thread: ${threadError.message}`);
    return null;
  }
  
  config.threadId = thread.id;
  
  // Add initial message from supplier
  const { error: msgError } = await supabase
    .from('task_messages')
    .insert({
      task_id: config.taskId,
      sender_id: config.supplierId,
      content: "Hi, I'm interested in this task and would like to discuss the requirements.",
      thread_id: thread.id
    });
  
  if (msgError) {
    warn(`Failed to create initial message: ${msgError.message}`);
  }
  
  // Add system message
  const { error: sysError } = await supabase
    .from('task_messages')
    .insert({
      task_id: config.taskId,
      sender_id: '********-0000-0000-0000-************', // System user
      content: 'A supplier has expressed interest in this task.',
      thread_id: thread.id
    });
  
  if (sysError) {
    warn(`Failed to create system message: ${sysError.message}`);
  }
  
  log('Supplier interest simulated successfully');
  return thread;
}

// Simulate admin response
async function simulateAdminResponse() {
  info('Simulating admin response...');
  
  const { error } = await supabase
    .from('task_messages')
    .insert({
      task_id: config.taskId,
      sender_id: config.adminId,
      content: "Thank you for your interest. What experience do you have with this type of work?",
      thread_id: config.threadId
    });
  
  if (error) {
    warn(`Failed to create admin response: ${error.message}`);
    return false;
  }
  
  log('Admin response simulated successfully');
  return true;
}

// Simulate supplier submitting an offer
async function simulateSupplierOffer() {
  info('Simulating supplier submitting an offer...');
  
  // Add supplier response
  const { error: msgError } = await supabase
    .from('task_messages')
    .insert({
      task_id: config.taskId,
      sender_id: config.supplierId,
      content: "I have 5 years of experience with similar projects. I'd like to submit a formal offer.",
      thread_id: config.threadId
    });
  
  if (msgError) {
    warn(`Failed to create supplier response: ${msgError.message}`);
  }
  
  // Create offer
  const { data: offer, error: offerError } = await supabase
    .from('offers')
    .insert({
      task_id: config.taskId,
      user_id: config.supplierId,
      amount: 95,
      message: "Based on our discussion, I can complete this work for £95. I'll ensure it meets all requirements and is delivered on time.",
      status: 'awaiting'
    })
    .select()
    .single();
  
  if (offerError) {
    error(`Failed to create offer: ${offerError.message}`);
    return null;
  }
  
  // Update thread status
  const { error: updateError } = await supabase
    .from('chat_threads')
    .update({
      status: 'questions',
      has_offer: true,
      updated_at: new Date().toISOString(),
      last_message_at: new Date().toISOString()
    })
    .eq('id', config.threadId);
  
  if (updateError) {
    warn(`Failed to update thread status: ${updateError.message}`);
  }
  
  // Add system messages
  const { error: sysError1 } = await supabase
    .from('task_messages')
    .insert({
      task_id: config.taskId,
      sender_id: '********-0000-0000-0000-************',
      content: `${config.supplierEmail} has submitted a formal offer of £95.00.`,
      thread_id: config.threadId
    });
  
  if (sysError1) {
    warn(`Failed to create system message about offer: ${sysError1.message}`);
  }
  
  const { error: sysError2 } = await supabase
    .from('task_messages')
    .insert({
      task_id: config.taskId,
      sender_id: '********-0000-0000-0000-************',
      content: 'Discussion phase has started. Please discuss requirements before submitting formal offers.',
      thread_id: config.threadId
    });
  
  if (sysError2) {
    warn(`Failed to create system message about questions phase: ${sysError2.message}`);
  }
  
  log('Supplier offer simulated successfully');
  log(`Offer ID: ${offer.id}`);
  return offer;
}

// Run the full test
async function runTest() {
  console.log(chalk.cyan('=== CHAT FLOW TEST ==='));
  
  try {
    await loginAsAdmin();
    const task = await createTestTask();
    if (!task) return;
    
    const supplier = await findSupplier();
    if (!supplier) return;
    
    const thread = await simulateExpressInterest();
    if (!thread) return;
    
    await sleep(1000); // Small delay between operations
    
    const adminResponseSuccess = await simulateAdminResponse();
    if (!adminResponseSuccess) return;
    
    await sleep(1000); // Small delay between operations
    
    const offer = await simulateSupplierOffer();
    if (!offer) return;
    
    console.log(chalk.green('\n✅ Test completed successfully!'));
    console.log(chalk.cyan('\nTest Summary:'));
    console.log(chalk.cyan(`- Task ID: ${config.taskId}`));
    console.log(chalk.cyan(`- Task URL: ${supabaseUrl.replace('.supabase.co', '.app')}/tasks/${config.taskId}`));
    console.log(chalk.cyan(`- Admin ID: ${config.adminId}`));
    console.log(chalk.cyan(`- Supplier ID: ${config.supplierId}`));
    console.log(chalk.cyan(`- Thread ID: ${config.threadId}`));
    console.log(chalk.cyan(`- Offer ID: ${offer.id}`));
    
    console.log(chalk.yellow('\nNext Steps for Manual Testing:'));
    console.log(chalk.yellow('1. Log in as admin and navigate to the task URL'));
    console.log(chalk.yellow('2. Check the Messages tab to see the conversation'));
    console.log(chalk.yellow('3. Accept the offer to test the assignment flow'));
    console.log(chalk.yellow('4. Log in as supplier to test the task completion flow'));
    
  } catch (err) {
    error(`Test failed: ${err.message}`);
    console.error(err);
  } finally {
    // Sign out
    await supabase.auth.signOut();
    log('Signed out');
  }
}

// Run the test
runTest();
