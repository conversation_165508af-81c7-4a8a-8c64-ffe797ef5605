
import { Link } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import TaskCard from '@/components/tasks/TaskCard';
import { Task } from '@/services/taskService';
import { useAuth } from '@/contexts/AuthContext';
import { useEffect } from 'react';

interface TasksListProps {
  tasks: Task[] | undefined;
  isLoading: boolean;
}

const TasksList = ({ tasks, isLoading }: TasksListProps) => {
  const { userRole } = useAuth();
  const isTeacher = userRole === 'teacher';

  // Log tasks for debugging
  useEffect(() => {
    if (isTeacher && tasks) {
      console.log('TasksList - Teacher tasks:', {
        totalTasks: tasks.length,
        taskDetails: tasks.map(t => ({
          id: t.id,
          title: t.title,
          visibility: t.visibility,
          status: t.status
        }))
      });
    }
  }, [isTeacher, tasks]);

  return (
    <Card>
      <CardContent className="p-6">
        <h2 className="text-xl font-semibold mb-6">
          {isTeacher ? "My Tasks (Including Pending Review)" : "My Posted Tasks"}
        </h2>
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Array(4).fill(0).map((_, i) => (
              <div key={i} className="border rounded-lg p-4">
                <Skeleton className="h-6 w-3/4 mb-2" />
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-5/6 mb-4" />
                <div className="flex justify-between">
                  <Skeleton className="h-4 w-1/4" />
                  <Skeleton className="h-4 w-1/4" />
                </div>
              </div>
            ))}
          </div>
        ) : tasks && tasks.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {tasks.map(task => (
              <Link
                key={task.id}
                to={`/tasks/${task.id}?messages=true`}
                className="block"
              >
                <TaskCard
                  id={task.id}
                  title={task.title}
                  description={task.description}
                  location={task.location}
                  dueDate={task.due_date}
                  budget={task.budget}
                  category={task.category}
                  status={task.status}
                  offers={task.offers_count}
                />
              </Link>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-500 mb-4">You haven't posted any tasks yet.</p>
            <Button className="bg-classtasker-blue hover:bg-blue-600" asChild>
              <Link to="/post-task">Post Your First Task</Link>
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default TasksList;
