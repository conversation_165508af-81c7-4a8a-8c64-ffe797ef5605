import { <PERSON><PERSON><PERSON><PERSON>, Clock, DollarSign, <PERSON>glass, Loader2, <PERSON><PERSON><PERSON>cle, ThumbsUp, User } from "lucide-react";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header, CardTitle } from "../ui/card";
import TaskStatusTimeline from "./TaskStatusTimeline";
import { useEffect, useState, useRef } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";

// Define the possible task statuses in order
// Note: We're keeping this for reference, but the actual status arrays are now in TaskStatusTimeline.tsx
const TASK_STATUSES = [
  'open',           // Task is created and open
  'interest',       // Suppliers have expressed interest
  'questions',      // Discussion phase between admin and suppliers
  'offer',          // Suppliers have submitted offers
  'assigned',       // Task has been assigned to someone
  'in_progress',    // Work has started on the task
  'completed',      // Work is completed, awaiting confirmation
  'closed',         // Task is closed by admin
  'pending_payment' // Payment is required to complete the task
];

// Define status descriptions for display
const STATUS_DESCRIPTIONS: Record<string, string> = {
  'open': 'Task has been created by the task creator and is awaiting interest from suppliers',
  'interest': 'Suppliers have expressed interest in this task and may submit offers',
  'questions': 'Suppliers are discussing requirements with the admin before submitting formal offers',
  'offer': 'Suppliers have submitted formal offers for this task, awaiting admin review',
  'assigned': 'Task has been assigned to a supplier and is ready to start',
  'in_progress': 'Task has been accepted by the supplier and work is currently being performed',
  'completed': 'Work has been marked as finished by the supplier and is awaiting admin approval',
  'closed': 'Task has been closed by the admin and is ready for payment',
  'pending_payment': 'Payment is required to complete this task'
};

interface TaskDetailTimelineProps {
  status: 'open' | 'interest' | 'questions' | 'offer' | 'assigned' | 'in_progress' | 'pending_payment' | 'completed' | 'confirmed';
  offersCount?: number;
  createdAt: string;
  updatedAt: string;
  assignedTo?: string;
  assignedToName?: string;
  className?: string;
  visibility?: 'admin' | 'internal' | 'public'; // Task visibility to determine workflow
}

const TaskDetailTimeline = ({
  status,
  offersCount = 0,
  createdAt,
  updatedAt,
  assignedTo,
  assignedToName,
  className,
  visibility = 'public' // Default to public for backward compatibility
}: TaskDetailTimelineProps) => {
  const { toast } = useToast();
  const [currentStatus, setCurrentStatus] = useState(status);
  const [isSubscribed, setIsSubscribed] = useState(false);
  const taskIdRef = useRef<string | null>(null);

  // Extract task ID from various sources
  useEffect(() => {
    // Try to extract from createdAt (which might contain task ID)
    let extractedId = null;

    if (createdAt) {
      // Try to extract from a string like "task_123456"
      const taskIdMatch = createdAt.match(/task_([0-9a-f-]+)/i);
      if (taskIdMatch) {
        extractedId = taskIdMatch[1];
      }

      // If that fails, try to extract from a URL or path
      if (!extractedId) {
        const urlMatch = createdAt.match(/tasks\/([0-9a-f-]+)/i);
        if (urlMatch) {
          extractedId = urlMatch[1];
        }
      }

      // If that fails and createdAt looks like a UUID, use it directly
      if (!extractedId && /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(createdAt)) {
        extractedId = createdAt;
      }
    }

    // Store the extracted ID
    if (extractedId && extractedId !== taskIdRef.current) {
      console.log('[TaskDetailTimeline] Extracted task ID:', extractedId);
      taskIdRef.current = extractedId;
    }
  }, [createdAt]);

  // Update state when props change
  useEffect(() => {
    if (status !== currentStatus) {
      console.log('[TaskDetailTimeline] Status prop changed from', currentStatus, 'to', status);
      setCurrentStatus(status);
    }
  }, [status, currentStatus]);

  // Set up real-time subscription for task status changes
  useEffect(() => {
    const taskId = taskIdRef.current;

    if (!taskId || isSubscribed) return;

    console.log('[TaskDetailTimeline] Setting up real-time subscription for task:', taskId);

    try {
      // Subscribe to task updates
      const taskChannel = supabase
        .channel(`task_timeline_${taskId}`)
        .on(
          'postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'tasks',
            filter: `id=eq.${taskId}`
          },
          (payload) => {
            console.log('[TaskDetailTimeline] Task updated:', payload);

            // Check for status changes
            if (payload.new && payload.new.status && payload.new.status !== currentStatus) {
              console.log('[TaskDetailTimeline] Updating status to:', payload.new.status);
              setCurrentStatus(payload.new.status);

              // Show a toast notification for status change
              toast({
                title: "Task Status Updated",
                description: `Task status changed to: ${payload.new.status.replace('_', ' ').toUpperCase()}`,
                duration: 3000
              });
            }

            // Check for visibility changes
            if (payload.new && payload.old && payload.new.visibility !== payload.old.visibility) {
              console.log('[TaskDetailTimeline] Visibility changed from', payload.old.visibility, 'to', payload.new.visibility);

              // Show a toast notification for visibility change
              toast({
                title: "Task Visibility Updated",
                description: `Task is now ${payload.new.visibility === 'public' ? 'visible to suppliers' : 'internal only'}`,
                duration: 3000
              });

              // Force a refresh of the task data
              window.location.reload();
            }
          }
        )
        .subscribe();

      // Also subscribe to chat thread updates for this task
      // This helps catch status changes that might not directly update the task
      const threadChannel = supabase
        .channel(`task_threads_${taskId}`)
        .on(
          'postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'chat_threads',
            filter: `task_id=eq.${taskId}`
          },
          async (payload) => {
            console.log('[TaskDetailTimeline] Thread updated:', payload);
            if (payload.new && payload.new.status) {
              // When a thread status changes, check if we need to refresh the task status
              try {
                const { data: taskData, error } = await supabase
                  .from('tasks')
                  .select('status')
                  .eq('id', taskId)
                  .single();

                if (error) {
                  console.error('[TaskDetailTimeline] Error fetching task after thread update:', error);
                  return;
                }

                if (taskData && taskData.status !== currentStatus) {
                  console.log('[TaskDetailTimeline] Task status changed after thread update:', taskData.status);
                  setCurrentStatus(taskData.status);

                  // Show a toast notification
                  toast({
                    title: "Task Status Updated",
                    description: `Task status changed to: ${taskData.status.replace('_', ' ').toUpperCase()}`,
                    duration: 3000
                  });
                }
              } catch (error) {
                console.error('[TaskDetailTimeline] Error handling thread update:', error);
              }
            }
          }
        )
        .subscribe();

      setIsSubscribed(true);

      return () => {
        console.log('[TaskDetailTimeline] Cleaning up subscriptions');
        supabase.removeChannel(taskChannel);
        supabase.removeChannel(threadChannel);
        setIsSubscribed(false);
      };
    } catch (error) {
      console.error('[TaskDetailTimeline] Error setting up subscription:', error);
    }
  }, [taskIdRef.current, isSubscribed, currentStatus, toast]);

  // Ensure we have a valid status, default to 'open' if not
  const validStatus = TASK_STATUSES.includes(currentStatus) ? currentStatus : 'open';

  // Find the current status index
  const currentStatusIndex = TASK_STATUSES.indexOf(validStatus);

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center">
          <div className="mr-2 p-1 rounded-full bg-blue-100">
            <Clock size={18} className="text-blue-600" />
          </div>
          Task Timeline
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Full timeline */}
        <div className="mb-6 mt-2">
          <TaskStatusTimeline
            status={validStatus}
            offersCount={offersCount}
            isCompact={false}
            visibility={visibility}
          />
        </div>

        {/* Current status details */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg border">
          <h3 className="font-medium text-lg mb-2 flex items-center">
            Current Status:
            <span className={
              validStatus === 'open' ? ' text-green-600 ml-2' :
              validStatus === 'interest' ? ' text-cyan-600 ml-2' :
              validStatus === 'questions' ? ' text-cyan-700 ml-2' :
              validStatus === 'offer' ? ' text-yellow-600 ml-2' :
              validStatus === 'assigned' ? ' text-blue-600 ml-2' :
              validStatus === 'in_progress' ? ' text-indigo-600 ml-2' :
              validStatus === 'pending_payment' ? ' text-yellow-600 ml-2' :
              validStatus === 'completed' ? ' text-purple-600 ml-2' :
              ' text-teal-600 ml-2'
            }>
              {validStatus === 'pending_payment' ? 'Payment Required' :
               validStatus === 'in_progress' ? 'In Progress' :
               validStatus === 'interest' ? 'Interest Expressed' :
               validStatus === 'questions' ? 'Discussion Phase' :
               validStatus === 'offer' ? 'Offers Received' :
               validStatus.charAt(0).toUpperCase() + validStatus.slice(1)}
            </span>
          </h3>
          <p className="text-gray-600 mb-4">{STATUS_DESCRIPTIONS[validStatus]}</p>

          {/* Status-specific details */}
          <div className="space-y-3 text-sm">
            <div className="flex justify-between items-center border-b pb-2">
              <span className="text-gray-500">Created:</span>
              <span className="font-medium">
                {createdAt ? format(new Date(createdAt), 'PPpp') : 'Not available'}
              </span>
            </div>

            <div className="flex justify-between items-center border-b pb-2">
              <span className="text-gray-500">Last Updated:</span>
              <span className="font-medium">
                {updatedAt ? format(new Date(updatedAt), 'PPpp') : 'Not available'}
              </span>
            </div>

            {validStatus !== 'open' && assignedTo && (
              <div className="flex justify-between items-center border-b pb-2">
                <span className="text-gray-500">Assigned To:</span>
                <span className="font-medium">{assignedToName || assignedTo}</span>
              </div>
            )}

            {(validStatus === 'open' || validStatus === 'offer') && offersCount > 0 && (
              <div className="flex justify-between items-center border-b pb-2">
                <span className="text-gray-500">Offers Received:</span>
                <span className="font-medium">{offersCount}</span>
              </div>
            )}
          </div>
        </div>

        {/* Next steps */}
        <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-100">
          <h3 className="font-medium text-lg mb-2 flex items-center text-blue-700">
            <div className="mr-2 p-1 rounded-full bg-blue-200">
              <Hourglass size={16} className="text-blue-700" />
            </div>
            Next Steps
          </h3>
          {validStatus === 'open' && (
            <p className="text-gray-700">
              Suppliers: Express interest in this task to start a conversation with the admin.
            </p>
          )}
          {validStatus === 'interest' && (
            <p className="text-gray-700">
              Suppliers and Admin: Discuss task requirements and details before submitting formal offers.
            </p>
          )}
          {validStatus === 'questions' && (
            <p className="text-gray-700">
              Suppliers: Submit formal offers after discussing requirements. Admin: Review conversations and wait for offers.
            </p>
          )}
          {validStatus === 'offer' && (
            <p className="text-gray-700">
              Admin: Review the offers from suppliers and select one to assign the task to. Suppliers: Wait for admin decision.
            </p>
          )}
          {validStatus === 'assigned' && (
            <p className="text-gray-700">
              Supplier: Accept the assignment and start work on the task.
            </p>
          )}
          {validStatus === 'in_progress' && (
            <p className="text-gray-700">
              Supplier: Complete the work and mark the task as "Completed" when finished.
            </p>
          )}
          {validStatus === 'completed' && (
            <p className="text-gray-700">
              Admin: Review the completed work and close it if it meets requirements.
            </p>
          )}
          {validStatus === 'confirmed' && (
            <p className="text-gray-700">
              {isInternalTask(task) ? 'Task has been completed and closed.' : 'Admin: Process payment for this task.'}
            </p>
          )}
          {validStatus === 'pending_payment' && (
            <p className="text-gray-700">
              Payment is being processed. Once complete, the task will be fully closed.
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default TaskDetailTimeline;
