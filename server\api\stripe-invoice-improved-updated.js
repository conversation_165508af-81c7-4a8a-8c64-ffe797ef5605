const express = require('express');
const router = express.Router();
const Stripe = require('stripe');
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

/**
 * Get or create a Stripe customer for a user
 * @param {string} userId - The user ID
 * @param {string} email - The user's email
 * @param {string} name - The user's name (optional)
 * @returns {Promise<string>} - The Stripe customer ID
 */
async function getOrCreateCustomer(userId, email, name) {
  try {
    // Check if the stripe_customers table exists
    let stripeCustomerId = null;
    
    try {
      // Check if the user already has a Stripe customer
      const { data: stripeCustomers, error: stripeCustomersError } = await supabase
        .from('stripe_customers')
        .select('customer_id')
        .eq('user_id', userId)
        .limit(1);

      if (stripeCustomersError) {
        console.error('Error fetching Stripe customer:', stripeCustomersError);
        
        // If the stripe_customers table doesn't exist, we'll create a new customer without storing it
        if (stripeCustomersError.code === '42P01') {
          console.warn('stripe_customers table does not exist. Creating a new Stripe customer without storing it.');
        } else {
          throw stripeCustomersError;
        }
      } else if (stripeCustomers && stripeCustomers.length > 0) {
        stripeCustomerId = stripeCustomers[0].customer_id;
        
        // Verify that the customer exists in Stripe
        try {
          await stripe.customers.retrieve(stripeCustomerId);
          return stripeCustomerId;
        } catch (error) {
          console.error('Error retrieving Stripe customer:', error);
          console.log('Creating a new Stripe customer...');
          stripeCustomerId = null;
        }
      }
    } catch (error) {
      console.error('Error checking for existing Stripe customer:', error);
      // Continue to create a new customer
    }
    
    // If we don't have a valid customer ID, create a new one
    if (!stripeCustomerId) {
      // Create a new Stripe customer
      const customer = await stripe.customers.create({
        email,
        name: name || email,
        metadata: {
          user_id: userId
        }
      });
      
      stripeCustomerId = customer.id;
      
      // Try to store the Stripe customer in the database
      try {
        await supabase
          .from('stripe_customers')
          .insert({
            user_id: userId,
            customer_id: stripeCustomerId,
            created_at: new Date().toISOString(),
          });
      } catch (error) {
        console.error('Error storing Stripe customer:', error);
        console.warn('Could not store Stripe customer in the database. The stripe_customers table might not exist.');
      }
    }
    
    return stripeCustomerId;
  } catch (error) {
    console.error('Error in getOrCreateCustomer:', error);
    throw error;
  }
}

/**
 * Create an invoice for a payment
 */
router.post('/create-invoice', async (req, res) => {
  try {
    const { paymentId } = req.body;
    
    if (!paymentId) {
      return res.status(400).json({ error: 'Payment ID is required' });
    }
    
    console.log(`Creating invoice for payment ${paymentId}`);
    
    // Get the payment details
    const { data: payment, error: paymentError } = await supabase
      .from('payments')
      .select(`
        *,
        tasks (
          id,
          title,
          description,
          user_id
        ),
        offers (
          id,
          user_id,
          amount
        )
      `)
      .eq('id', paymentId)
      .single();
    
    if (paymentError) {
      console.error('Error fetching payment:', paymentError);
      return res.status(500).json({ error: 'Error fetching payment' });
    }
    
    if (!payment) {
      return res.status(404).json({ error: 'Payment not found' });
    }
    
    // Get the payer details
    const { data: payer, error: payerError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', payment.payer_id)
      .single();
    
    if (payerError) {
      console.error('Error fetching payer:', payerError);
      return res.status(500).json({ error: 'Error fetching payer' });
    }
    
    if (!payer) {
      return res.status(404).json({ error: 'Payer not found' });
    }
    
    // Get or create a Stripe customer for the payer
    const customerEmail = Array.isArray(payer.email) ? payer.email[0] : payer.email;
    const customerName = payer.full_name || customerEmail;
    const customerId = await getOrCreateCustomer(payment.payer_id, customerEmail, customerName);
    
    // Check if an invoice already exists for this payment
    const { data: existingInvoices, error: existingInvoicesError } = await supabase
      .from('invoices')
      .select('stripe_invoice_id')
      .eq('payment_id', paymentId);
    
    if (existingInvoicesError) {
      console.error('Error checking for existing invoices:', existingInvoicesError);
      return res.status(500).json({ error: 'Error checking for existing invoices' });
    }
    
    if (existingInvoices && existingInvoices.length > 0) {
      // An invoice already exists, retrieve it from Stripe
      try {
        const existingInvoice = await stripe.invoices.retrieve(existingInvoices[0].stripe_invoice_id);
        return res.json(existingInvoice);
      } catch (error) {
        console.error('Error retrieving existing invoice:', error);
        // Continue to create a new invoice
      }
    }
    
    // Create an invoice item
    const invoiceItem = await stripe.invoiceItems.create({
      customer: customerId,
      amount: Math.round(payment.amount * 100), // Convert to cents
      currency: payment.currency,
      description: `Payment for: ${payment.tasks.title}`,
    });
    
    console.log(`Created invoice item: ${invoiceItem.id}`);
    
    // Create an invoice
    const invoice = await stripe.invoices.create({
      customer: customerId,
      collection_method: 'send_invoice',
      days_until_due: 30,
      metadata: {
        payment_id: payment.id,
        task_id: payment.task_id,
        offer_id: payment.offer_id,
      },
    });
    
    console.log(`Created invoice: ${invoice.id}`);
    
    // Finalize the invoice
    const finalizedInvoice = await stripe.invoices.finalizeInvoice(invoice.id);
    
    console.log(`Finalized invoice: ${finalizedInvoice.id}`);
    console.log(`Invoice number: ${finalizedInvoice.number}`);
    
    // Store the invoice in our database
    const { data: invoiceRecord, error: invoiceError } = await supabase
      .from('invoices')
      .insert([{
        payment_id: paymentId,
        invoice_number: finalizedInvoice.number, // Use the Stripe-generated invoice number
        invoice_url: finalizedInvoice.hosted_invoice_url,
        stripe_invoice_id: finalizedInvoice.id,
        status: finalizedInvoice.status,
        due_date: finalizedInvoice.due_date ? new Date(finalizedInvoice.due_date * 1000).toISOString() : null,
      }])
      .select();
    
    if (invoiceError) {
      console.error('Error storing invoice in database:', invoiceError);
      return res.status(500).json({ error: 'Error storing invoice in database' });
    }
    
    return res.json(finalizedInvoice);
  } catch (error) {
    console.error('Error creating invoice:', error);
    return res.status(500).json({ error: 'Error creating invoice' });
  }
});

/**
 * Get an invoice by ID
 */
router.get('/invoice/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({ error: 'Invoice ID is required' });
    }
    
    console.log(`Getting invoice ${id}`);
    
    // Get the invoice from Stripe
    const invoice = await stripe.invoices.retrieve(id);
    
    return res.json(invoice);
  } catch (error) {
    console.error('Error getting invoice:', error);
    return res.status(500).json({ error: 'Error getting invoice' });
  }
});

/**
 * Get the PDF URL for an invoice
 */
router.get('/invoice/:id/pdf', async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({ error: 'Invoice ID is required' });
    }
    
    console.log(`Getting PDF URL for invoice ${id}`);
    
    // Get the invoice from Stripe
    const invoice = await stripe.invoices.retrieve(id, {
      expand: ['invoice_pdf'],
    });
    
    return res.json({ url: invoice.invoice_pdf });
  } catch (error) {
    console.error('Error getting invoice PDF URL:', error);
    return res.status(500).json({ error: 'Error getting invoice PDF URL' });
  }
});

/**
 * Send an invoice email
 */
router.post('/send-invoice-email', async (req, res) => {
  try {
    const { invoiceId } = req.body;
    
    if (!invoiceId) {
      return res.status(400).json({ error: 'Invoice ID is required' });
    }
    
    console.log(`Sending email for invoice ${invoiceId}`);
    
    // Get the invoice from our database
    const { data: invoice, error: invoiceError } = await supabase
      .from('invoices')
      .select('stripe_invoice_id')
      .eq('id', invoiceId)
      .single();
    
    if (invoiceError) {
      console.error('Error fetching invoice:', invoiceError);
      return res.status(500).json({ error: 'Error fetching invoice' });
    }
    
    if (!invoice) {
      return res.status(404).json({ error: 'Invoice not found' });
    }
    
    // Send the invoice email
    const sentInvoice = await stripe.invoices.sendInvoice(invoice.stripe_invoice_id);
    
    return res.json(sentInvoice);
  } catch (error) {
    console.error('Error sending invoice email:', error);
    return res.status(500).json({ error: 'Error sending invoice email' });
  }
});

module.exports = router;
