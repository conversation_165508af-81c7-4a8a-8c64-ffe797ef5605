/**
 * GetStream API Endpoints Test
 * 
 * This script tests the GetStream API endpoints:
 * - /api/getstream/token
 * - /api/getstream/channels
 * - /api/getstream/system-message
 * 
 * Run with: node src/tests/getstream-api-test.js
 */

import fetch from 'node-fetch';

// Configuration
const BASE_URL = 'http://localhost:3000';
const TEST_PREFIX = 'api-test-' + Date.now();
const TEST_USER_ID = `${TEST_PREFIX}-user`;
const TEST_TASK_ID = `${TEST_PREFIX}-task`;
const TEST_TASK_TITLE = 'Test Task for API Testing';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  total: 0,
};

// Helper function to log test results
function logTest(name, passed, message = '') {
  testResults.total++;
  
  if (passed) {
    testResults.passed++;
    console.log(`${colors.green}✓ PASS${colors.reset} ${name}`);
    if (message) {
      console.log(`  ${colors.dim}${message}${colors.reset}`);
    }
  } else {
    testResults.failed++;
    console.log(`${colors.red}✗ FAIL${colors.reset} ${name}`);
    if (message) {
      console.log(`  ${colors.red}${message}${colors.reset}`);
    }
  }
}

// Helper function to log section headers
function logSection(title) {
  console.log(`\n${colors.bright}${colors.cyan}=== ${title} ===${colors.reset}`);
}

// Test the token endpoint
async function testTokenEndpoint() {
  logSection('Testing Token Endpoint');
  
  // Test with valid user ID
  try {
    const response = await fetch(`${BASE_URL}/api/getstream/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userId: TEST_USER_ID }),
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    
    const data = await response.json();
    
    if (data.token) {
      logTest('Token generation (valid user)', true, `Token: ${data.token.substring(0, 20)}...`);
    } else {
      logTest('Token generation (valid user)', false, 'No token in response');
    }
  } catch (error) {
    logTest('Token generation (valid user)', false, error.message);
  }
  
  // Test with missing user ID
  try {
    const response = await fetch(`${BASE_URL}/api/getstream/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({}),
    });
    
    if (response.status === 400) {
      logTest('Token generation (missing user)', true, 'Correctly returned 400 for missing user ID');
    } else {
      logTest('Token generation (missing user)', false, `Expected 400, got ${response.status}`);
    }
  } catch (error) {
    logTest('Token generation (missing user)', false, error.message);
  }
}

// Test the channels endpoint
async function testChannelsEndpoint() {
  logSection('Testing Channels Endpoint');
  
  // Test with valid data
  try {
    const response = await fetch(`${BASE_URL}/api/getstream/channels`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        taskId: TEST_TASK_ID,
        taskTitle: TEST_TASK_TITLE,
        members: [TEST_USER_ID],
      }),
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    
    const data = await response.json();
    
    if (data.channelId) {
      logTest('Channel creation (valid data)', true, `Channel ID: ${data.channelId}, Status: ${data.status}`);
      return data.channelId;
    } else {
      logTest('Channel creation (valid data)', false, 'No channelId in response');
      return null;
    }
  } catch (error) {
    logTest('Channel creation (valid data)', false, error.message);
    return null;
  }
  
  // Test with missing task ID
  try {
    const response = await fetch(`${BASE_URL}/api/getstream/channels`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        taskTitle: TEST_TASK_TITLE,
        members: [TEST_USER_ID],
      }),
    });
    
    if (response.status === 400) {
      logTest('Channel creation (missing task ID)', true, 'Correctly returned 400 for missing task ID');
    } else {
      logTest('Channel creation (missing task ID)', false, `Expected 400, got ${response.status}`);
    }
  } catch (error) {
    logTest('Channel creation (missing task ID)', false, error.message);
  }
}

// Test the system message endpoint
async function testSystemMessageEndpoint(channelId) {
  logSection('Testing System Message Endpoint');
  
  if (!channelId) {
    logTest('System message (valid data)', false, 'No channel ID available');
    return;
  }
  
  // Test with valid data
  try {
    const response = await fetch(`${BASE_URL}/api/getstream/system-message`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        channelId,
        text: `This is a test system message from the API test suite (${TEST_PREFIX})`,
      }),
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    
    const data = await response.json();
    
    if (data.success) {
      logTest('System message (valid data)', true, 'Message sent successfully');
    } else {
      logTest('System message (valid data)', false, 'Not successful');
    }
  } catch (error) {
    logTest('System message (valid data)', false, error.message);
  }
  
  // Test with missing channel ID
  try {
    const response = await fetch(`${BASE_URL}/api/getstream/system-message`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        text: `This is a test system message from the API test suite (${TEST_PREFIX})`,
      }),
    });
    
    if (response.status === 400) {
      logTest('System message (missing channel ID)', true, 'Correctly returned 400 for missing channel ID');
    } else {
      logTest('System message (missing channel ID)', false, `Expected 400, got ${response.status}`);
    }
  } catch (error) {
    logTest('System message (missing channel ID)', false, error.message);
  }
  
  // Test with missing text
  try {
    const response = await fetch(`${BASE_URL}/api/getstream/system-message`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        channelId,
      }),
    });
    
    if (response.status === 400) {
      logTest('System message (missing text)', true, 'Correctly returned 400 for missing text');
    } else {
      logTest('System message (missing text)', false, `Expected 400, got ${response.status}`);
    }
  } catch (error) {
    logTest('System message (missing text)', false, error.message);
  }
}

// Run all tests
async function runTests() {
  console.log(`${colors.bright}${colors.magenta}=== GetStream API Endpoints Test ===${colors.reset}`);
  console.log(`Base URL: ${BASE_URL}`);
  console.log(`Test Prefix: ${TEST_PREFIX}`);
  console.log(`Test User ID: ${TEST_USER_ID}`);
  console.log(`Test Task ID: ${TEST_TASK_ID}`);
  console.log(`${colors.magenta}======================================${colors.reset}\n`);
  
  // Run tests in sequence
  await testTokenEndpoint();
  const channelId = await testChannelsEndpoint();
  await testSystemMessageEndpoint(channelId);
  
  // Print summary
  logSection('Test Summary');
  console.log(`Total tests: ${testResults.total}`);
  console.log(`${colors.green}Passed: ${testResults.passed}${colors.reset}`);
  console.log(`${colors.red}Failed: ${testResults.failed}${colors.reset}`);
  
  if (testResults.failed === 0) {
    console.log(`\n${colors.green}${colors.bright}All tests passed!${colors.reset}`);
  } else {
    console.log(`\n${colors.red}${colors.bright}Some tests failed!${colors.reset}`);
  }
}

// Run the tests
runTests();
