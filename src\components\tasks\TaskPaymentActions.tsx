import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Loader2, CreditCard } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { Task, Offer } from '@/services/taskService';
import SimplePaymentProcessor from '@/components/stripe/SimplePaymentProcessor';

interface TaskPaymentActionsProps {
  task: Task;
  acceptedOffer: Offer | null;
  onTaskUpdated: () => void;
}

const TaskPaymentActions = ({ task, acceptedOffer, onTaskUpdated }: TaskPaymentActionsProps) => {
  const { toast } = useToast();
  const { isAdmin } = useAuth();
  const [showPaymentDialog, setShowPaymentDialog] = useState(false);

  // Only show this component for task owners or admins with tasks in confirmed or pending_payment status
  if (!task || (task.status !== 'confirmed' && task.status !== 'pending_payment') || !acceptedOffer) {
    return null;
  }

  const handlePaymentSuccess = () => {
    setShowPaymentDialog(false);
    toast({
      title: "Payment successful",
      description: "Your payment has been processed successfully. The task is now complete.",
      variant: "default",
    });
    onTaskUpdated();
  };

  const handlePaymentCancel = () => {
    setShowPaymentDialog(false);
  };

  return (
    <>
      <Card className={`mb-6 ${task.status === 'pending_payment' ? 'border-yellow-300' : 'border-green-300'}`}>
        <CardHeader>
          <CardTitle>{task.status === 'pending_payment' ? 'Payment Required' : 'Ready for Payment'}</CardTitle>
          <CardDescription>
            {task.status === 'pending_payment'
              ? 'This task is marked as complete and requires payment to finalize'
              : 'The task has been approved and is ready for payment processing'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button
            onClick={() => setShowPaymentDialog(true)}
            className={`w-full ${task.status === 'pending_payment'
              ? 'bg-yellow-600 hover:bg-yellow-700'
              : 'bg-green-600 hover:bg-green-700'}`}
          >
            <CreditCard className="mr-2 h-4 w-4" /> Complete Payment (£{Number(acceptedOffer.amount).toFixed(2)})
          </Button>
        </CardContent>
      </Card>

      <Dialog open={showPaymentDialog} onOpenChange={setShowPaymentDialog}>
        <DialogContent className="sm:max-w-[800px] w-[90vw]">
          <DialogHeader>
            <DialogTitle>Complete Payment</DialogTitle>
            <DialogDescription>
              Pay for the completed task to release funds to the supplier.
            </DialogDescription>
          </DialogHeader>

          {acceptedOffer && (
            <SimplePaymentProcessor
              taskId={task.id}
              offerId={acceptedOffer.id}
              amount={Number(acceptedOffer.amount)}
              onSuccess={handlePaymentSuccess}
              onCancel={handlePaymentCancel}
            />
          )}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default TaskPaymentActions;
