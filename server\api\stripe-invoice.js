import express from 'express';
import { createClient } from '@supabase/supabase-js';
import Stripe from 'stripe';

const router = express.Router();
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Create an invoice for a payment
router.post('/create-invoice', async (req, res) => {
  try {
    const { paymentId } = req.body;

    if (!paymentId) {
      return res.status(400).json({ error: 'Payment ID is required' });
    }

    console.log('Creating invoice for payment', paymentId);

    // Get the payment details
    const { data: payment, error: paymentError } = await supabase
      .from('payments')
      .select(`
        *,
        tasks (*),
        offers (*)
      `)
      .eq('id', paymentId)
      .single();

    if (paymentError || !payment) {
      console.error('Error fetching payment:', paymentError);
      return res.status(404).json({ error: 'Payment not found' });
    }

    // Get the payer details
    const { data: payer, error: payerError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', payment.payer_id)
      .single();

    if (payerError || !payer) {
      console.error('Error fetching payer:', payerError);
      return res.status(404).json({ error: 'Payer not found' });
    }

    // Get the payee details
    const { data: payee, error: payeeError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', payment.payee_id)
      .single();

    if (payeeError || !payee) {
      console.error('Error fetching payee:', payeeError);
      return res.status(404).json({ error: 'Payee not found' });
    }

    // Get the organization details
    const { data: organization, error: orgError } = await supabase
      .from('organizations')
      .select('*')
      .eq('id', payer.organization_id)
      .single();

    if (orgError) {
      console.error('Error fetching organization:', orgError);
      // Continue without organization details
    }

    // Create a customer in Stripe if needed
    let customer;

    // Check if the payer already has a Stripe customer ID
    if (payer.stripe_customer_id) {
      // Get the existing customer
      customer = await stripe.customers.retrieve(payer.stripe_customer_id);
    } else {
      // Create a new customer
      customer = await stripe.customers.create({
        email: payer.email,
        name: payer.first_name && payer.last_name
          ? `${payer.first_name} ${payer.last_name}`
          : payer.email,
        metadata: {
          user_id: payer.id,
          organization_id: payer.organization_id
        }
      });

      // Save the customer ID to the user's profile
      await supabase
        .from('profiles')
        .update({ stripe_customer_id: customer.id })
        .eq('id', payer.id);
    }

    // Format the task title and description
    const taskTitle = payment.tasks?.title || 'Task Payment';
    const taskDescription = payment.tasks?.description || 'Payment for completed task';

    // Create an invoice
    const invoice = await stripe.invoices.create({
      customer: customer.id,
      collection_method: 'send_invoice',
      days_until_due: 30, // Due in 30 days
      metadata: {
        payment_id: payment.id,
        task_id: payment.task_id,
        offer_id: payment.offer_id
      }
    });

    // Add invoice items
    await stripe.invoiceItems.create({
      customer: customer.id,
      invoice: invoice.id,
      amount: Math.round(payment.amount * 100), // Convert to cents
      currency: payment.currency || 'gbp',
      description: `Payment for: ${taskTitle}`,
    });

    // Finalize the invoice
    const finalizedInvoice = await stripe.invoices.finalizeInvoice(invoice.id);

    // Send the invoice
    const sentInvoice = await stripe.invoices.sendInvoice(invoice.id);

    // Retrieve the invoice with PDF URL
    const retrievedInvoice = await stripe.invoices.retrieve(sentInvoice.id);

    // Store the invoice in our database
    const { data: invoiceRecord, error: invoiceError } = await supabase
      .from('invoices')
      .insert([{
        payment_id: payment.id,
        invoice_number: sentInvoice.number,
        stripe_invoice_id: sentInvoice.id,
        invoice_url: retrievedInvoice.invoice_pdf || sentInvoice.hosted_invoice_url,
        status: sentInvoice.status,
        due_date: sentInvoice.due_date ? new Date(sentInvoice.due_date * 1000).toISOString() : null,
      }])
      .select();

    if (invoiceError) {
      console.error('Error storing invoice in database:', invoiceError);
      // Continue anyway since the invoice was created in Stripe
    }

    // Update the payment record with the invoice ID
    await supabase
      .from('payments')
      .update({
        metadata: {
          ...payment.metadata,
          stripe_invoice_id: sentInvoice.id
        }
      })
      .eq('id', payment.id);

    return res.status(200).json(sentInvoice);
  } catch (error) {
    console.error('Error creating invoice:', error);
    return res.status(500).json({ error: 'Failed to create invoice' });
  }
});

// Get an invoice by ID
router.get('/invoice/:id', async (req, res) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({ error: 'Invoice ID is required' });
    }

    console.log('Getting invoice', id);

    // Get the invoice from Stripe
    const invoice = await stripe.invoices.retrieve(id);

    return res.status(200).json(invoice);
  } catch (error) {
    console.error('Error getting invoice:', error);
    return res.status(500).json({ error: 'Failed to get invoice' });
  }
});

// Get the PDF URL for an invoice
router.get('/invoice/:id/pdf', async (req, res) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({ error: 'Invoice ID is required' });
    }

    console.log('Getting PDF URL for invoice', id);

    // Get the invoice from Stripe
    const invoice = await stripe.invoices.retrieve(id);

    // Stripe doesn't provide a direct PDF URL through the API
    // Instead, we use the hosted_invoice_url which shows the invoice in the browser
    // From there, users can download the PDF

    // The hosted invoice URL looks like this:
    // https://invoice.stripe.com/i/acct_XXX/test_YYY?s=ap

    // We'll return the hosted invoice URL
    return res.status(200).json({ url: invoice.hosted_invoice_url });
  } catch (error) {
    console.error('Error getting invoice PDF URL:', error);
    return res.status(500).json({ error: 'Failed to get invoice PDF URL' });
  }
});

// List invoices for a customer
router.get('/invoices/:customerId', async (req, res) => {
  try {
    const { customerId } = req.params;

    if (!customerId) {
      return res.status(400).json({ error: 'Customer ID is required' });
    }

    console.log('Listing invoices for customer', customerId);

    // Get the invoices from Stripe
    const invoices = await stripe.invoices.list({
      customer: customerId,
      limit: 100
    });

    return res.status(200).json(invoices.data);
  } catch (error) {
    console.error('Error listing invoices:', error);
    return res.status(500).json({ error: 'Failed to list invoices' });
  }
});

// Send an invoice email
router.post('/invoice/:id/send', async (req, res) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({ error: 'Invoice ID is required' });
    }

    console.log('Sending email for invoice', id);

    // Get the invoice from Stripe
    const invoice = await stripe.invoices.retrieve(id);

    // Send the invoice email
    const sentInvoice = await stripe.invoices.sendInvoice(id);

    return res.status(200).json({ sent: true, invoice: sentInvoice });
  } catch (error) {
    console.error('Error sending invoice email:', error);
    return res.status(500).json({ error: 'Failed to send invoice email' });
  }
});

export default router;
