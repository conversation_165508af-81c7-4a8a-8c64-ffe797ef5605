# ===================================
# COMPREHENSIVE .GITIGNORE FOR PRODUCTION
# ===================================

# Logs and debugging
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Environment variables - SECURITY CRITICAL
.env
.env.local
.env.development
.env.production
.env.test
.env.staging
.env.*
!.env.example

# Dependencies and build outputs
node_modules
dist
dist-ssr
*.local
.vercel

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# ===================================
# DEVELOPMENT AND TEST FILES
# ===================================

# Test scripts and utilities (SECURITY: Prevent accidental key exposure)
test-*.js
test-*.cjs
test-*.ts
test-*.html
check-*.js
check-*.cjs
debug-*.js
debug-*.cjs
fix-*.js
fix-*.cjs
apply-*.js
apply-*.cjs
modify-*.js
modify-*.cjs
verify-*.js
verify-*.cjs
create-test-*.js
create-test-*.cjs
run-test.*
run-chat-test.*
run-admin-test.*

# Security and audit scripts
security-*.js
security-*.cjs
security-*.ts
audit-*.js
audit-*.cjs
*security-check*
*security-audit*
*security-cleanup*

# Temporary and backup files
*.tmp
*.temp
*.bak
*.backup
*.bak[0-9]
*.element-fix
*.fixed
*-backup.*
commit-message.txt
terminal-command

# Development documentation and notes
*-debug-changes.md
*-troubleshooting.md
project_status.md
git-workflow-guide.md

# ===================================
# KEEP IMPORTANT PRODUCTION FILES
# ===================================

# Keep essential scripts
!apply-invoices-update.js
!activate-stripe-account.js
!scripts/test-invitation-load.js
!scripts/remove-console-logs.js
!scripts/final-security-check.js

# Keep essential documentation
!README.md
!SECURITY.md
!docs/README.md

# ===================================
# MEDIA AND ASSETS
# ===================================

# Test images and videos
test-image.*
test-video.*
scripts/test-image.*
scripts/videos/
videos/
*.webm
*.mp4
*.avi

# Screenshots and dumps
*.png
*.jpg
*.jpeg
!public/icons/*.svg
!public/icons/*.png
tasks-dump.json

# ===================================
# SPECIFIC FILE PATTERNS
# ===================================

# Stripe backup files
src/components/stripe/*.bak*
src/components/stripe/*.backup
src/components/stripe/*.element-fix
src/components/stripe/*.fixed

# Task completion backup files
src/components/tasks/*.bak*
src/pages/fix-task-completion-*

# SQL dumps and test files
*.sql.bak
*-dump.sql
test_scripts/

# Edge function backups
*edge-function*.txt
*edge-function*.md

# Batch files and shell scripts (development only)
*.bat
*.sh
!deploy-*.sh

# ===================================
# SUPABASE TEMPORARY FILES
# ===================================

# Supabase CLI temporary files
supabase/.temp/
supabase/.temp/*
supabase/functions/test-*/

# Additional environment file protection
server/.env*
scripts/.env*
api/.env*

# ===================================
# PACKAGE MANAGER FILES
# ===================================

# Lock files (keep main one, ignore others)
scripts/package-lock.json
server/package-lock.json
src/server/package.json

# ===================================
# COMPLIANCE AND NOTIFICATIONS
# ===================================

# Compliance notification archives
compliance-notifications.zip
*-notifications.zip
