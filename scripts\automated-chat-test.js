/**
 * Automated Chat Flow Test Script
 * 
 * This script helps test the chat flow by automating the creation of test data.
 * Run this in the browser console while logged in as admin.
 * 
 * IMPORTANT: This is for testing purposes only and should not be used in production.
 */

// Configuration
const config = {
  taskId: '', // Will be set after task creation
  adminId: '', // Will be set from current user
  supplierId: '', // Will be populated from the supplier account
  supplierEmail: '<EMAIL>', // Replace with your test supplier email
};

// Utility functions
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));
const log = (message) => console.log(`%c${message}`, 'color: #4CAF50; font-weight: bold;');
const error = (message) => console.error(`%c${message}`, 'color: #F44336; font-weight: bold;');
const warn = (message) => console.warn(`%c${message}`, 'color: #FF9800; font-weight: bold;');
const info = (message) => console.info(`%c${message}`, 'color: #2196F3; font-weight: bold;');

// Main test function
async function runChatFlowTest() {
  log('Starting Chat Flow Test');
  
  try {
    // Get current user
    const { data: { user } } = await window.supabase.auth.getUser();
    if (!user) {
      error('You must be logged in as admin to run this test');
      return;
    }
    
    config.adminId = user.id;
    log(`Admin ID: ${config.adminId}`);
    
    // Step 1: Create a test task
    await createTestTask();
    
    // Step 2: Find or create supplier account
    await setupSupplierAccount();
    
    // Step 3: Simulate supplier expressing interest
    await simulateExpressInterest();
    
    // Step 4: Simulate admin response
    await simulateAdminResponse();
    
    // Step 5: Simulate supplier submitting offer
    await simulateSupplierOffer();
    
    log('Chat Flow Test completed successfully!');
    log(`Task ID: ${config.taskId}`);
    log('Please continue manual testing from here using the task ID above.');
    
  } catch (err) {
    error('Test failed with error:');
    console.error(err);
  }
}

// Step 1: Create a test task
async function createTestTask() {
  log('Creating test task...');
  
  const taskData = {
    title: `Chat Flow Test Task ${new Date().toISOString().slice(0, 16)}`,
    description: 'This is a test task to verify the chat flow functionality.',
    category: 'Maintenance',
    budget: 100,
    due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 1 week from now
    location: 'Test Location',
    visibility: 'public',
    user_id: config.adminId,
    status: 'open',
  };
  
  const { data: task, error: taskError } = await window.supabase
    .from('tasks')
    .insert(taskData)
    .select()
    .single();
  
  if (taskError) {
    error('Failed to create test task');
    throw taskError;
  }
  
  config.taskId = task.id;
  log(`Test task created with ID: ${config.taskId}`);
  
  // Create initial system message
  await window.supabase.rpc('create_task_message', {
    task_id_param: config.taskId,
    sender_id_param: '********-0000-0000-0000-************', // System user
    content_param: 'Task created and is now open for offers.'
  });
  
  return task;
}

// Step 2: Find or create supplier account
async function setupSupplierAccount() {
  log('Setting up supplier account...');
  
  // Find supplier by email
  const { data: supplierData, error: supplierError } = await window.supabase
    .from('profiles')
    .select('id, email')
    .filter('email', 'cs', `{${config.supplierEmail}}`)
    .single();
  
  if (supplierError) {
    warn(`Supplier with email ${config.supplierEmail} not found. Please ensure this account exists.`);
    return;
  }
  
  config.supplierId = supplierData.id;
  log(`Found supplier with ID: ${config.supplierId}`);
  
  return supplierData;
}

// Step 3: Simulate supplier expressing interest
async function simulateExpressInterest() {
  if (!config.supplierId) {
    warn('Cannot simulate express interest: Supplier ID not found');
    return;
  }
  
  log('Simulating supplier expressing interest...');
  
  // Create chat thread
  const { data: threadData, error: threadError } = await window.supabase
    .from('chat_threads')
    .insert({
      task_id: config.taskId,
      supplier_id: config.supplierId,
      admin_id: config.adminId,
      status: 'interest',
      has_offer: false
    })
    .select()
    .single();
  
  if (threadError) {
    error('Failed to create chat thread');
    throw threadError;
  }
  
  log(`Chat thread created with ID: ${threadData.id}`);
  
  // Send initial message from supplier
  const initialMessage = "Hi, I'm interested in this task and would like to discuss the requirements.";
  
  const { error: messageError } = await window.supabase
    .from('task_messages')
    .insert({
      task_id: config.taskId,
      sender_id: config.supplierId,
      content: initialMessage,
      thread_id: threadData.id
    });
  
  if (messageError) {
    error('Failed to create initial message');
    throw messageError;
  }
  
  // Add system message about expressing interest
  const { error: systemError } = await window.supabase
    .from('task_messages')
    .insert({
      task_id: config.taskId,
      sender_id: '********-0000-0000-0000-************', // System user
      content: 'A supplier has expressed interest in this task.',
      thread_id: threadData.id
    });
  
  if (systemError) {
    error('Failed to create system message');
    throw systemError;
  }
  
  log('Supplier interest simulated successfully');
  return threadData;
}

// Step 4: Simulate admin response
async function simulateAdminResponse() {
  log('Simulating admin response...');
  
  // Get the thread ID
  const { data: threadData, error: threadError } = await window.supabase
    .from('chat_threads')
    .select('id')
    .eq('task_id', config.taskId)
    .eq('supplier_id', config.supplierId)
    .single();
  
  if (threadError) {
    error('Failed to find chat thread');
    throw threadError;
  }
  
  // Send admin response
  const adminResponse = "Thank you for your interest. What experience do you have with this type of work?";
  
  const { error: messageError } = await window.supabase
    .from('task_messages')
    .insert({
      task_id: config.taskId,
      sender_id: config.adminId,
      content: adminResponse,
      thread_id: threadData.id
    });
  
  if (messageError) {
    error('Failed to create admin response');
    throw messageError;
  }
  
  log('Admin response simulated successfully');
}

// Step 5: Simulate supplier submitting offer
async function simulateSupplierOffer() {
  log('Simulating supplier offer...');
  
  // Get the thread ID
  const { data: threadData, error: threadError } = await window.supabase
    .from('chat_threads')
    .select('id')
    .eq('task_id', config.taskId)
    .eq('supplier_id', config.supplierId)
    .single();
  
  if (threadError) {
    error('Failed to find chat thread');
    throw threadError;
  }
  
  // Send supplier response
  const supplierResponse = "I have 5 years of experience with similar projects. Based on our discussion, I'd like to submit a formal offer.";
  
  const { error: messageError } = await window.supabase
    .from('task_messages')
    .insert({
      task_id: config.taskId,
      sender_id: config.supplierId,
      content: supplierResponse,
      thread_id: threadData.id
    });
  
  if (messageError) {
    error('Failed to create supplier response');
    throw messageError;
  }
  
  // Create offer
  const { data: offerData, error: offerError } = await window.supabase
    .from('offers')
    .insert({
      task_id: config.taskId,
      user_id: config.supplierId,
      amount: 95,
      message: "Based on our discussion, I can complete this work for £95. I'll ensure it meets all requirements and is delivered on time.",
      status: 'awaiting'
    })
    .select()
    .single();
  
  if (offerError) {
    error('Failed to create offer');
    throw offerError;
  }
  
  log(`Offer created with ID: ${offerData.id}`);
  
  // Update thread status
  const { error: updateError } = await window.supabase
    .from('chat_threads')
    .update({
      status: 'questions',
      has_offer: true,
      updated_at: new Date().toISOString(),
      last_message_at: new Date().toISOString()
    })
    .eq('id', threadData.id);
  
  if (updateError) {
    error('Failed to update thread status');
    throw updateError;
  }
  
  // Add system message about the offer
  const systemMessage = `${config.supplierEmail} has submitted a formal offer of £95.00.`;
  
  const { error: systemError1 } = await window.supabase
    .from('task_messages')
    .insert({
      task_id: config.taskId,
      sender_id: '********-0000-0000-0000-************', // System user
      content: systemMessage,
      thread_id: threadData.id
    });
  
  if (systemError1) {
    error('Failed to create system message about offer');
    throw systemError1;
  }
  
  // Add system message about questions phase
  const { error: systemError2 } = await window.supabase
    .from('task_messages')
    .insert({
      task_id: config.taskId,
      sender_id: '********-0000-0000-0000-************', // System user
      content: 'Discussion phase has started. Please discuss requirements before submitting formal offers.',
      thread_id: threadData.id
    });
  
  if (systemError2) {
    error('Failed to create system message about questions phase');
    throw systemError2;
  }
  
  log('Supplier offer simulated successfully');
}

// Instructions for running the test
console.log('%c=== CHAT FLOW TEST SCRIPT ===', 'color: #9C27B0; font-weight: bold; font-size: 16px;');
console.log('%cTo run the test, call the function: runChatFlowTest()', 'color: #FF5722; font-weight: bold;');
console.log('%cMake sure you are logged <NAME_EMAIL>', 'color: #FF5722; font-weight: bold;');
console.log('%cReplace <EMAIL> with your actual test supplier email in the config', 'color: #FF5722; font-weight: bold;');

// Export the test function to global scope
window.runChatFlowTest = runChatFlowTest;
