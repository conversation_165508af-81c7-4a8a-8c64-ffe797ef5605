import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Loader2, CheckCircle, AlertCircle, FileText } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { Task, Offer } from '@/services/taskService';
import { stripeService } from '@/services/stripeService';
import systemMessageService from '@/services/systemMessageService';
import streamSystemMessages from '@/utils/streamSystemMessages';
import { getStreamChannelForTask } from '@/utils/getStreamChannel';
import EnhancedPaymentProcessor from '@/components/stripe/EnhancedPaymentProcessor';

interface TaskCompletionActionsProps {
  task: Task;
  acceptedOffer: Offer | null;
  onTaskUpdated: () => void;
}

const TaskCompletionActions = ({ task, acceptedOffer, onTaskUpdated }: TaskCompletionActionsProps) => {
  const { toast } = useToast();
  const { isAdmin } = useAuth();
  const [isMarkingComplete, setIsMarkingComplete] = useState(false);
  const [showPaymentDialog, setShowPaymentDialog] = useState(false);

  // Only show this component for task owners or admins with completed or confirmed tasks
  // Note: 'assigned' status is now handled by the supplier in SupplierActions component
  if (!task || (task.status !== 'completed' && task.status !== 'confirmed')) {
    return null;
  }

  // Debug info for troubleshooting
  console.log('TaskCompletionActions Debug:', {
    isAdmin,
    taskStatus: task.status,
    taskVisibility: task.visibility,
    acceptedOffer: !!acceptedOffer,
    offers: acceptedOffer ? 'Has accepted offer' : 'No accepted offer'
  });

  // Check if there's an accepted offer
  if (!acceptedOffer) {
    return null;
  }

  const handleMarkComplete = async () => {
    try {
      setIsMarkingComplete(true);

      console.log('Handling task status change:', {
        taskId: task.id,
        currentStatus: task.status,
        isAdmin,
        userId: (await supabase.auth.getUser()).data.user?.id
      });

      let newStatus = '';
      let toastMessage = '';
      let showPayment = false;

      // Determine the next status based on current status
      if (task.status === 'completed') {
        // Admin approving completed work - mark as closed
        newStatus = 'closed';
        toastMessage = "Task approved. You can now proceed to payment.";
        showPayment = true;
      } else if (task.status === 'closed') {
        // Admin processing payment - mark as pending_payment
        newStatus = 'pending_payment';
        toastMessage = "Task marked as ready for payment. You can now complete the payment.";
        showPayment = true;
      }

      if (!newStatus) {
        throw new Error("Invalid task status transition");
      }

      // Update the task status
      const updateData: any = {
        status: newStatus,
        updated_at: new Date().toISOString()
      };

      // Add payment_status only when moving to pending_payment
      if (newStatus === 'pending_payment') {
        updateData.payment_status = 'pending';
      }

      const { error } = await supabase
        .from('tasks')
        .update(updateData)
        .eq('id', task.id);

      if (error) {
        console.error('Error updating task status:', error);
        throw error;
      }

      // Create a payment record for the task if moving to pending_payment
      if (newStatus === 'pending_payment' && acceptedOffer) {
        const payment = await stripeService.createPaymentWithDirectTransfer(
          task.id,
          acceptedOffer.id,
          Number(acceptedOffer.amount)
        );

        if (payment) {
          // Generate an invoice for the payment
          try {
            const invoice = await stripeService.createInvoice(payment.id);
            if (invoice) {
              console.log(`Invoice ${invoice.invoice_number} created successfully`);
              toast({
                title: "Invoice Generated",
                description: "An invoice has been generated and sent to your email.",
                variant: "default",
              });
            }
          } catch (invoiceError) {
            console.error('Error generating invoice:', invoiceError);
            // Continue even if invoice generation fails
          }
        }
      }

      toast({
        title: "Task status updated",
        description: toastMessage,
        variant: "default",
      });

      // Show payment dialog if needed
      if (showPayment) {
        setShowPaymentDialog(true);
      }

      // Get active thread ID if available
      let activeThreadId: string | undefined;
      try {
        const { data: threadData } = await supabase
          .from('chat_threads')
          .select('id')
          .eq('task_id', task.id)
          .eq('supplier_id', task.assigned_to || '')
          .maybeSingle();

        activeThreadId = threadData?.id;
      } catch (error) {
        console.error('Error fetching thread ID:', error);
      }

      // Send system message about status change
      // First try to get the GetStream channel
      const channel = await getStreamChannelForTask(task.id);

      if (channel) {
        // If we have a GetStream channel, use the streamSystemMessages utility
        console.log(`Using GetStream for ${newStatus} system message`);
        await streamSystemMessages.createStatusChangeStreamMessage(
          task.id,
          newStatus,
          channel,
          task.assigned_to,
          false // Not an internal task
        );
      } else {
        // Fallback to the old method if channel not available
        console.log(`Fallback to legacy system message for ${newStatus}`);
        await systemMessageService.createStatusChangeMessage(
          task.id,
          newStatus,
          task.assigned_to,
          activeThreadId
        );
      }

      // Refresh task data
      onTaskUpdated();
    } catch (error) {
      console.error('Error updating task status:', error);
      toast({
        title: "Error",
        description: "Failed to update task status. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsMarkingComplete(false);
    }
  };

  const handlePaymentSuccess = () => {
    setShowPaymentDialog(false);
    toast({
      title: "Payment successful",
      description: "Your payment has been processed successfully. The task is now complete.",
      variant: "default",
    });
    onTaskUpdated();
  };

  const handlePaymentCancel = () => {
    setShowPaymentDialog(false);
  };

  return (
    <>
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>
            {task.status === 'completed' ? 'Approve Completed Task' : 'Process Payment'}
          </CardTitle>
          <CardDescription>
            {task.status === 'completed' ? 'Approve the completed work if it meets your requirements' :
             'Process payment for the approved task'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="mb-2 text-sm text-gray-500">
            {isAdmin ? 'Admin action: You are managing this task on behalf of the task owner.' : ''}
          </div>
          <Button
            onClick={handleMarkComplete}
            className={`w-full ${
              task.status === 'completed' ? 'bg-green-600 hover:bg-green-700' :
              'bg-yellow-600 hover:bg-yellow-700'
            }`}
            disabled={isMarkingComplete}
          >
            {isMarkingComplete ? (
              <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Processing...</>
            ) : (
              <>
                {task.status === 'completed' ? (
                  <><CheckCircle className="mr-2 h-4 w-4" /> Approve Completed Work</>
                ) : (
                  <><FileText className="mr-2 h-4 w-4" /> Process Payment</>
                )}
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      <Dialog open={showPaymentDialog} onOpenChange={setShowPaymentDialog}>
        <DialogContent className="sm:max-w-[800px] w-[90vw]">
          <DialogHeader>
            <DialogTitle>Complete Payment</DialogTitle>
            <DialogDescription>
              Pay for the completed task to release funds to the supplier.
            </DialogDescription>
          </DialogHeader>

          {acceptedOffer && (
            <EnhancedPaymentProcessor
              taskId={task.id}
              offerId={acceptedOffer.id}
              amount={Number(acceptedOffer.amount)}
              onSuccess={handlePaymentSuccess}
              onCancel={handlePaymentCancel}
            />
          )}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default TaskCompletionActions;
