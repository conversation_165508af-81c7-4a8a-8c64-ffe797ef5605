import express from 'express';
import Stripe from 'stripe';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import cors from 'cors';

dotenv.config();

const router = express.Router();
router.use(cors());
router.use(express.json());

// Initialize Stripe with the secret key from environment variables
const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
const stripePublicKey = process.env.STRIPE_PUBLIC_KEY;

// Log only the first few and last few characters of the keys for security
if (stripeSecretKey) {
  console.log('Stripe Secret API key:', `${stripeSecretKey.substring(0, 7)}...${stripeSecretKey.substring(stripeSecretKey.length - 4)}`);
  console.log('API mode:', stripeSecretKey.startsWith('sk_test') ? 'TEST' : 'LIVE');
} else {
  console.error('Stripe secret key is not defined in environment variables');
}

if (stripePublicKey) {
  console.log('Stripe Public API key:', `${stripePublicKey.substring(0, 7)}...${stripePublicKey.substring(stripePublicKey.length - 4)}`);
} else {
  console.error('Stripe public key is not defined in environment variables');
}

// Validate required environment variables
if (!stripeSecretKey || !stripePublicKey) {
  console.error('Missing required Stripe environment variables');
  process.exit(1);
}

console.log('Using Stripe API keys:', stripeSecretKey.startsWith('sk_test') ? 'TEST MODE' : 'LIVE MODE');

const stripe = new Stripe(stripeSecretKey);

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceRoleKey) {
  console.error('Supabase URL or service role key is not defined in environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);

// Create a Stripe Connect Express account link
router.post('/create-account-link', async (req, res) => {
  try {
    const { accountId, refreshUrl, returnUrl } = req.body;

    if (!accountId) {
      return res.status(400).json({ error: 'Account ID is required' });
    }

    const accountLink = await stripe.accountLinks.create({
      account: accountId,
      refresh_url: refreshUrl || process.env.STRIPE_CONNECT_EXPRESS_REFRESH_URL,
      return_url: returnUrl || process.env.STRIPE_CONNECT_EXPRESS_RETURN_URL,
      type: 'account_onboarding',
    });

    res.json({ url: accountLink.url });
  } catch (error) {
    console.error('Error creating account link:', error);
    res.status(500).json({ error: 'Failed to create account link' });
  }
});

// Create a Stripe Connect Express onboarding link (alias for create-account-link)
router.post('/onboarding-link', async (req, res) => {
  try {
    const { accountId, refreshUrl, returnUrl } = req.body;

    if (!accountId) {
      return res.status(400).json({ error: 'Account ID is required' });
    }

    console.log('Creating onboarding link for account:', accountId);

    const accountLink = await stripe.accountLinks.create({
      account: accountId,
      refresh_url: refreshUrl || process.env.STRIPE_CONNECT_EXPRESS_REFRESH_URL,
      return_url: returnUrl || process.env.STRIPE_CONNECT_EXPRESS_RETURN_URL,
      type: 'account_onboarding',
    });

    console.log('Created onboarding link:', accountLink.url);
    res.json({ url: accountLink.url });
  } catch (error) {
    console.error('Error creating onboarding link:', error);
    res.status(500).json({ error: 'Failed to create onboarding link' });
  }
});

// Create a Stripe Connect Express account
router.post('/create-account', async (req, res) => {
  try {
    const { userId, email, country = 'GB' } = req.body;

    if (!userId || !email) {
      return res.status(400).json({ error: 'User ID and email are required' });
    }

    // Check if the user already has a Stripe account
    const { data: existingAccounts } = await supabase
      .from('stripe_accounts')
      .select('*')
      .eq('user_id', userId);

    if (existingAccounts && existingAccounts.length > 0) {
      return res.json({ accountId: existingAccounts[0].account_id });
    }

    // Create a new Stripe Connect Express account
    const account = await stripe.accounts.create({
      type: 'express',
      country,
      email,
      capabilities: {
        card_payments: { requested: true },
        transfers: { requested: true },
      },
      business_type: 'individual',
      business_profile: {
        mcc: '8299', // Educational Services
        url: 'https://lovable.dev',
      },
    });

    // Store the account in the database
    const { data, error } = await supabase
      .from('stripe_accounts')
      .insert({
        user_id: userId,
        account_id: account.id,
        account_type: 'express',
        account_status: 'pending',
      });

    if (error) {
      console.error('Error storing Stripe account in database:', error);
      return res.status(500).json({ error: 'Failed to store Stripe account' });
    }

    res.json({ accountId: account.id });
  } catch (error) {
    console.error('Error creating Stripe account:', error);
    res.status(500).json({ error: 'Failed to create Stripe account' });
  }
});

// Get Stripe Connect Express account status
router.get('/account-status/:accountId', async (req, res) => {
  try {
    const { accountId } = req.params;
    console.log('Getting status for account', accountId);

    if (!accountId) {
      return res.status(400).json({ error: 'Account ID is required' });
    }

    // Verify the account exists in our database
    const { data: dbAccount, error: dbError } = await supabase
      .from('stripe_accounts')
      .select('*')
      .eq('account_id', accountId)
      .single();

    if (dbError || !dbAccount) {
      console.error('Error fetching account from database:', dbError);
      return res.status(404).json({ error: 'Account not found in database' });
    }

    console.log('Retrieving Stripe Connect Express account status...');
    try {
      const account = await stripe.accounts.retrieve(accountId);

      // Update the account status in the database
      const accountStatus = account.charges_enabled ? 'active' : 'pending';

      const { error: updateError } = await supabase
        .from('stripe_accounts')
        .update({
          charges_enabled: account.charges_enabled,
          payouts_enabled: account.payouts_enabled,
          account_status: accountStatus,
          updated_at: new Date().toISOString()
        })
        .eq('account_id', accountId);

      if (updateError) {
        console.error('Error updating Stripe account status in database:', updateError);
      }

      return res.json({
        id: account.id,
        charges_enabled: account.charges_enabled,
        payouts_enabled: account.payouts_enabled,
        requirements: account.requirements,
        status: accountStatus,
      });
    } catch (stripeError) {
      console.error('Error retrieving account from Stripe:', stripeError);

      // If we can't retrieve from Stripe, return the database record
      return res.json({
        id: dbAccount.account_id,
        charges_enabled: dbAccount.charges_enabled,
        payouts_enabled: dbAccount.payouts_enabled,
        requirements: { currently_due: [], eventually_due: [], past_due: [] },
        status: dbAccount.account_status,
        fromDatabase: true
      });
    }
  } catch (error) {
    console.error('Error getting Stripe account status:', error);
    res.status(500).json({ error: 'Failed to get Stripe account status' });
  }
});

// Create a login link for a Stripe Connect Express account
router.post('/create-login-link', async (req, res) => {
  try {
    const { accountId } = req.body;

    if (!accountId) {
      return res.status(400).json({ error: 'Account ID is required' });
    }

    const loginLink = await stripe.accounts.createLoginLink(accountId);

    res.json({ url: loginLink.url });
  } catch (error) {
    console.error('Error creating login link:', error);
    res.status(500).json({ error: 'Failed to create login link' });
  }
});

// Create a dashboard link for a Stripe Connect Express account (alias for create-login-link)
router.post('/dashboard-link', async (req, res) => {
  try {
    const { accountId } = req.body;

    if (!accountId) {
      return res.status(400).json({ error: 'Account ID is required' });
    }

    console.log('Creating dashboard link for account:', accountId);

    // Verify the account exists in our database
    const { data: dbAccount, error: dbError } = await supabase
      .from('stripe_accounts')
      .select('*')
      .eq('account_id', accountId)
      .single();

    if (dbError || !dbAccount) {
      console.error('Error fetching account from database:', dbError);
      return res.status(404).json({ error: 'Account not found in database' });
    }

    try {
      // Try to create a login link (only works for accounts that have completed onboarding)
      const loginLink = await stripe.accounts.createLoginLink(accountId);
      console.log('Created dashboard link:', loginLink.url);
      return res.json({ url: loginLink.url, isOnboarding: false });
    } catch (loginError) {
      console.log('Could not create login link, falling back to onboarding link');

      try {
        // If creating a login link fails, create an onboarding link instead
        const accountLink = await stripe.accountLinks.create({
          account: accountId,
          refresh_url: process.env.STRIPE_CONNECT_EXPRESS_REFRESH_URL || 'http://localhost:8082/stripe-connect',
          return_url: process.env.STRIPE_CONNECT_EXPRESS_RETURN_URL || 'http://localhost:8082/stripe-connect',
          type: 'account_onboarding',
        });

        console.log('Created onboarding link as fallback:', accountLink.url);
        return res.json({ url: accountLink.url, isOnboarding: true });
      } catch (accountLinkError) {
        console.error('Error creating account link:', accountLinkError);

        // If all else fails, return a link to the Stripe dashboard
        return res.json({
          url: 'https://dashboard.stripe.com/test/dashboard',
          isOnboarding: false,
          isFallback: true
        });
      }
    }
  } catch (error) {
    console.error('Error creating dashboard link:', error);
    res.status(500).json({ error: 'Failed to create dashboard link' });
  }
});

// Create a payment with direct transfer
router.post('/create-payment', async (req, res) => {
  try {
    const { taskId, offerId, amount } = req.body;
    const { user_id: payerId } = req.body.user;

    if (!taskId || !offerId || !amount || !payerId) {
      return res.status(400).json({ error: 'Task ID, offer ID, amount, and payer ID are required' });
    }

    // Get the offer details to find the supplier (payee)
    const { data: offer, error: offerError } = await supabase
      .from('offers')
      .select('user_id')
      .eq('id', offerId)
      .single();

    if (offerError || !offer) {
      console.error('Error getting offer details:', offerError);
      return res.status(500).json({ error: 'Failed to get offer details' });
    }

    const payeeId = offer.user_id;

    // Calculate platform fee (20% of the amount)
    const platformFeePercentage = process.env.PLATFORM_FEE_PERCENTAGE || 20;
    const platformFee = (amount * platformFeePercentage) / 100;
    const supplierAmount = amount - platformFee;

    // Create a payment record
    const { data: payment, error: paymentError } = await supabase
      .from('payments')
      .insert({
        task_id: taskId,
        offer_id: offerId,
        payer_id: payerId,
        payee_id: payeeId,
        amount,
        platform_fee: platformFee,
        supplier_amount: supplierAmount,
        status: 'pending',
        currency: 'gbp', // Default to GBP
      })
      .select()
      .single();

    if (paymentError || !payment) {
      console.error('Error creating payment record:', paymentError);
      return res.status(500).json({ error: 'Failed to create payment record' });
    }

    res.json(payment);
  } catch (error) {
    console.error('Error creating payment:', error);
    res.status(500).json({ error: 'Failed to create payment' });
  }
});

// Create a payment intent for a payment
router.post('/create-payment-intent', async (req, res) => {
  try {
    const { paymentId } = req.body;
    console.log('=== CREATE PAYMENT INTENT REQUEST ===');
    console.log('Request body:', req.body);
    console.log('Request headers:', req.headers);

    if (!paymentId) {
      return res.status(400).json({ error: 'Payment ID is required' });
    }

    console.log('Creating payment intent for payment', paymentId);

    // Get the payment details
    console.log('Fetching payment details from Supabase...');
    const { data: payment, error: paymentError } = await supabase
      .from('payments')
      .select(`
        *,
        tasks (*),
        offers (*)
      `)
      .eq('id', paymentId)
      .single();

    if (paymentError || !payment) {
      console.error('Error getting payment details:', paymentError);
      return res.status(500).json({ error: 'Failed to get payment details' });
    }

    console.log('Payment details:', payment);

    // For direct payments, we don't need the supplier's Stripe account
    console.log('Using direct payment for supplier', payment.payee_id);

    // Skip Stripe account check for direct payments

    // Convert amount to cents for Stripe
    const amountInCents = Math.round(payment.amount * 100);
    const platformFeeInCents = Math.round(payment.platform_fee * 100);

    // Simplify payment method types to just card for now to fix the issue
    // We'll add back the other payment methods once the basic functionality works
    const paymentMethodTypes = ['card'];
    const currency = payment.currency.toLowerCase();

    console.log('Using simplified payment method types for debugging:', paymentMethodTypes);

    // Configure payment method options
    // Keep this simple to avoid API errors
    const paymentMethodOptions = {
      card: {}
    };

    // Create a DIRECT payment intent without Connect functionality to fix the error
    // We'll add back the Connect functionality once the basic payment flow works
    const paymentIntentParams = {
      amount: amountInCents,
      currency: payment.currency,
      payment_method_types: ['card'], // Only use card to fix the issue
      // Removed transfer_data and application_fee_amount for now
      metadata: {
        payment_id: paymentId,
        task_id: payment.task_id,
        offer_id: payment.offer_id,
      }
      // Removed payment_method_options to simplify
    };

    console.log('IMPORTANT: Using direct payment intent without Connect functionality for testing');

    console.log('Creating payment intent with params:', JSON.stringify(paymentIntentParams, null, 2));

    try {
      const paymentIntent = await stripe.paymentIntents.create(paymentIntentParams);
      console.log('Payment intent created successfully:', {
        id: paymentIntent.id,
        clientSecret: `${paymentIntent.client_secret.substring(0, 10)}...`,
        status: paymentIntent.status,
        amount: paymentIntent.amount,
        currency: paymentIntent.currency
      });

      // Update the payment record with the payment intent ID
      await supabase
        .from('payments')
        .update({
          payment_intent_id: paymentIntent.id,
          status: 'processing',
        })
        .eq('id', paymentId);

      // Return the client secret
      return res.json({ clientSecret: paymentIntent.client_secret });
    } catch (stripeError) {
      console.error('Stripe API error creating payment intent:', stripeError);
      return res.status(400).json({
        error: 'Error creating payment intent',
        details: stripeError.message
      });
    }
  } catch (error) {
    console.error('Error creating payment intent:', error);
    return res.status(500).json({ error: 'Error creating payment intent' });
  }
});

// Create an invoice for a payment
router.post('/create-invoice', async (req, res) => {
  try {
    const { paymentId } = req.body;

    if (!paymentId) {
      return res.status(400).json({ error: 'Payment ID is required' });
    }

    console.log('Creating invoice for payment', paymentId);

    // Get the payment details
    const { data: payment, error: paymentError } = await supabase
      .from('payments')
      .select(`
        *,
        tasks (*),
        offers (*)
      `)
      .eq('id', paymentId)
      .single();

    if (paymentError || !payment) {
      console.error('Error getting payment details:', paymentError);
      return res.status(500).json({ error: 'Failed to get payment details' });
    }

    // Get the payer details
    const { data: payer, error: payerError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', payment.payer_id)
      .single();

    if (payerError || !payer) {
      console.error('Error getting payer details:', payerError);
      return res.status(500).json({ error: 'Failed to get payer details' });
    }

    // Create or get a Stripe customer for the payer
    let customer;

    try {
      // Check if customer already exists
      const customers = await stripe.customers.list({
        email: payer.email[0], // Email is stored as an array
        limit: 1
      });

      if (customers.data.length > 0) {
        customer = customers.data[0];
        console.log(`Using existing customer: ${customer.id}`);
      } else {
        // Create a new customer
        customer = await stripe.customers.create({
          email: payer.email[0],
          name: `${payer.first_name} ${payer.last_name}` || payer.email[0],
          metadata: {
            user_id: payer.id
          }
        });
        console.log(`Created new customer: ${customer.id}`);
      }
    } catch (error) {
      console.error('Error creating/getting Stripe customer:', error);
      return res.status(500).json({ error: 'Failed to create/get Stripe customer' });
    }

    // Create an invoice item
    const invoiceItem = await stripe.invoiceItems.create({
      customer: customer.id,
      amount: Math.round(payment.amount * 100), // Convert to cents
      currency: payment.currency || 'gbp',
      description: `Payment for: ${payment.tasks.title}`,
    });

    console.log(`Created invoice item: ${invoiceItem.id}`);

    // Create an invoice
    const invoice = await stripe.invoices.create({
      customer: customer.id,
      collection_method: 'send_invoice',
      days_until_due: 30,
      metadata: {
        payment_id: payment.id,
        task_id: payment.task_id,
        offer_id: payment.offer_id
      }
    });

    console.log(`Created invoice: ${invoice.id}`);

    // Finalize the invoice
    const finalizedInvoice = await stripe.invoices.finalizeInvoice(invoice.id);

    console.log(`Finalized invoice: ${finalizedInvoice.id}`);
    console.log(`Invoice number: ${finalizedInvoice.number}`);

    // Send the invoice
    const sentInvoice = await stripe.invoices.sendInvoice(finalizedInvoice.id);

    console.log(`Sent invoice: ${sentInvoice.id}`);
    console.log(`Invoice URL: ${sentInvoice.hosted_invoice_url}`);

    return res.json({
      id: sentInvoice.id,
      number: sentInvoice.number,
      hosted_invoice_url: sentInvoice.hosted_invoice_url,
      status: sentInvoice.status,
      due_date: sentInvoice.due_date
    });
  } catch (error) {
    console.error('Error creating invoice:', error);
    return res.status(500).json({ error: 'Failed to create invoice' });
  }
});

// Send an invoice email
router.post('/invoice/:id/send', async (req, res) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({ error: 'Invoice ID is required' });
    }

    console.log('Sending email for invoice', id);

    // Get the invoice from Stripe
    const invoice = await stripe.invoices.retrieve(id);

    // Send the invoice email
    const sentInvoice = await stripe.invoices.sendInvoice(id);

    return res.status(200).json({ sent: true, invoice: sentInvoice });
  } catch (error) {
    console.error('Error sending invoice email:', error);
    return res.status(500).json({ error: 'Failed to send invoice email' });
  }
});

// Create a Stripe Checkout session
router.post('/create-checkout-session', async (req, res) => {
  try {
    const { paymentId, successUrl, cancelUrl } = req.body;

    if (!paymentId) {
      return res.status(400).json({ error: 'Payment ID is required' });
    }

    console.log('Creating checkout session for payment', paymentId);

    // Get the payment from the database
    const { data: payment, error: paymentError } = await supabase
      .from('payments')
      .select('*')
      .eq('id', paymentId)
      .single();

    if (paymentError || !payment) {
      console.error('Error fetching payment:', paymentError);
      return res.status(404).json({ error: 'Payment not found' });
    }

    // Create a Stripe Checkout session
    console.log('Creating Stripe Checkout session with the following parameters:');
    const sessionParams = {
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: payment.currency,
            product_data: {
              name: `Payment for Task #${payment.task_id.substring(0, 8)}`,
              description: 'Task payment via ClassTasker',
            },
            unit_amount: Math.round(payment.amount * 100), // Convert to cents
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: successUrl || `${process.env.FRONTEND_URL || 'http://localhost:8082'}/payment-success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: cancelUrl || `${process.env.FRONTEND_URL || 'http://localhost:8082'}/payment-cancel`,
      metadata: {
        payment_id: payment.id,
        task_id: payment.task_id,
        offer_id: payment.offer_id,
      },
    };
    console.log('Session parameters:', JSON.stringify(sessionParams, null, 2));
    const session = await stripe.checkout.sessions.create(sessionParams);

    // Update the payment record with the session ID
    await supabase
      .from('payments')
      .update({
        payment_intent_id: session.payment_intent,
        metadata: { checkout_session_id: session.id },
        updated_at: new Date().toISOString(),
      })
      .eq('id', paymentId);

    // Use the Stripe public key from environment variables
    // Only log a masked version of the key for security
    if (stripePublicKey) {
      console.log('Using Stripe public key for checkout:', `${stripePublicKey.substring(0, 7)}...${stripePublicKey.substring(stripePublicKey.length - 4)}`);
    }

    return res.status(200).json({
      sessionId: session.id,
      publicKey: stripePublicKey
    });
  } catch (error) {
    console.error('Error creating checkout session:', error);
    return res.status(500).json({ error: 'Failed to create checkout session' });
  }
});

// Delete a Stripe Connect Express account
router.delete('/delete-account/:accountId', async (req, res) => {
  try {
    const { accountId } = req.params;
    console.log('Deleting Stripe account', accountId);

    if (!accountId) {
      return res.status(400).json({ error: 'Account ID is required' });
    }

    // Verify the account exists in our database
    const { data: dbAccount, error: dbError } = await supabase
      .from('stripe_accounts')
      .select('*')
      .eq('account_id', accountId)
      .single();

    if (dbError || !dbAccount) {
      console.error('Error fetching account from database:', dbError);
      return res.status(404).json({ error: 'Account not found in database' });
    }

    try {
      // Try to delete the account from Stripe
      const deletedAccount = await stripe.accounts.del(accountId);
      console.log('Deleted account from Stripe:', deletedAccount);

      // Update the account status in the database
      const { error: updateError } = await supabase
        .from('stripe_accounts')
        .update({
          account_status: 'deleted',
          updated_at: new Date().toISOString()
        })
        .eq('account_id', accountId);

      if (updateError) {
        console.error('Error updating account status in database:', updateError);
        return res.status(500).json({ error: 'Failed to update account status in database' });
      }

      return res.json({ deleted: true });
    } catch (stripeError) {
      console.error('Error deleting account from Stripe:', stripeError);

      // If we can't delete from Stripe, still mark as deleted in our database
      const { error: updateError } = await supabase
        .from('stripe_accounts')
        .update({
          account_status: 'deleted',
          updated_at: new Date().toISOString()
        })
        .eq('account_id', accountId);

      if (updateError) {
        console.error('Error updating account status in database:', updateError);
        return res.status(500).json({ error: 'Failed to update account status in database' });
      }

      return res.json({
        deleted: true,
        warning: 'Account marked as deleted in database, but could not be deleted from Stripe'
      });
    }
  } catch (error) {
    console.error('Error deleting Stripe account:', error);
    return res.status(500).json({ error: 'Failed to delete Stripe account' });
  }
});

export default router;
