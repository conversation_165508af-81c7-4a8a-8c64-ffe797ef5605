/**
 * GetStream Vercel Test
 *
 * This script tests the GetStream API endpoints running on the Vercel MCP server.
 * Run with: node src/tests/getstream-vercel-test.js
 */

// Use fetch for making HTTP requests
import fetch from 'node-fetch';

// Test user ID
const TEST_USER_ID = 'test-user-' + Date.now();

// Base URL for the Vercel MCP server
const BASE_URL = 'http://localhost:3000';

// Test the token endpoint
async function testTokenEndpoint() {
  console.log('Testing GetStream token endpoint...');

  try {
    const response = await fetch(`${BASE_URL}/api/getstream/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userId: TEST_USER_ID }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data = await response.json();

    if (data.token) {
      console.log('✅ Token endpoint working!');
      console.log(`Token: ${data.token.substring(0, 20)}...`);
      return data.token;
    } else {
      console.error('❌ Token endpoint failed: No token in response');
      console.error('Response:', data);
      return null;
    }
  } catch (error) {
    console.error('❌ Token endpoint failed:', error.message);
    return null;
  }
}

// Test the channels endpoint
async function testChannelsEndpoint(token) {
  console.log('\nTesting GetStream channels endpoint...');

  if (!token) {
    console.error('❌ Skipping channels test: No token available');
    return;
  }

  try {
    const taskId = 'test-task-' + Date.now();
    const taskTitle = 'Test Task';

    const response = await fetch(`${BASE_URL}/api/getstream/channels`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        taskId,
        taskTitle,
        members: [TEST_USER_ID],
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data = await response.json();

    if (data.channelId) {
      console.log('✅ Channels endpoint working!');
      console.log(`Channel ID: ${data.channelId}`);
      console.log(`Status: ${data.status}`);
      return data.channelId;
    } else {
      console.error('❌ Channels endpoint failed: No channelId in response');
      console.error('Response:', data);
      return null;
    }
  } catch (error) {
    console.error('❌ Channels endpoint failed:', error.message);
    return null;
  }
}

// Test the system message endpoint
async function testSystemMessageEndpoint(channelId) {
  console.log('\nTesting GetStream system message endpoint...');

  if (!channelId) {
    console.error('❌ Skipping system message test: No channel ID available');
    return;
  }

  try {
    const response = await fetch(`${BASE_URL}/api/getstream/system-message`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        channelId,
        text: 'This is a test system message',
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data = await response.json();

    if (data.success) {
      console.log('✅ System message endpoint working!');
      console.log('Message sent successfully');
    } else {
      console.error('❌ System message endpoint failed: Not successful');
      console.error('Response:', data);
    }
  } catch (error) {
    console.error('❌ System message endpoint failed:', error.message);
  }
}

// Run all tests
async function runTests() {
  console.log('=== GetStream Vercel MCP Server Test ===');
  console.log(`Base URL: ${BASE_URL}`);
  console.log(`Test User ID: ${TEST_USER_ID}`);
  console.log('======================================\n');

  const token = await testTokenEndpoint();
  const channelId = await testChannelsEndpoint(token);
  await testSystemMessageEndpoint(channelId);

  console.log('\n=== Test Complete ===');
}

// Run the tests
runTests();
