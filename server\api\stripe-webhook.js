import express from 'express';
import Stripe from 'stripe';
import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config();

const router = express.Router();

// Initialize Stripe with the secret key
const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
const stripeWebhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

if (!stripeSecretKey || !stripeWebhookSecret) {
  console.error('Missing Stripe secret key or webhook secret. Check your environment variables.');
  process.exit(1);
}

console.log(`Webhook - Stripe API key: ${stripeSecretKey.substring(0, 8)}...${stripeSecretKey.substring(stripeSecretKey.length - 4)}`);
console.log(`Webhook - API mode: ${stripeSecretKey.startsWith('sk_test_') ? 'TEST' : 'LIVE'}`);

const stripe = new Stripe(stripeSecretKey, {
  apiVersion: '2025-03-31.basil',
});

// Initialize Supabase client with service role key
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase URL or service role key. Check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Webhook handler
router.post('/', express.raw({ type: 'application/json' }), async (req, res) => {
  const sig = req.headers['stripe-signature'];

  if (!sig) {
    return res.status(400).send('Missing Stripe signature');
  }

  let event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, stripeWebhookSecret);
  } catch (err) {
    console.error(`Webhook Error: ${err.message}`);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  console.log(`Received webhook event: ${event.type}`);

  try {
    switch (event.type) {
      case 'account.updated': {
        const account = event.data.object;

        console.log(`Account ${account.id} updated`);

        // Update our database with the latest account status
        await supabase
          .from('stripe_accounts')
          .update({
            charges_enabled: account.charges_enabled,
            payouts_enabled: account.payouts_enabled,
            account_status: account.charges_enabled && account.payouts_enabled ? 'active' : 'pending',
            updated_at: new Date().toISOString(),
          })
          .eq('account_id', account.id);

        break;
      }

      case 'payment_intent.succeeded': {
        const paymentIntent = event.data.object;

        console.log(`Payment intent ${paymentIntent.id} succeeded`);

        // Update the payment record
        const { data: payment, error: paymentError } = await supabase
          .from('payments')
          .update({
            status: 'succeeded',
            updated_at: new Date().toISOString(),
          })
          .eq('payment_intent_id', paymentIntent.id)
          .select()
          .single();

        if (paymentError) {
          console.error('Error updating payment record:', paymentError);
        }

        // Update the task status
        if (paymentIntent.metadata?.task_id) {
          await supabase
            .from('tasks')
            .update({
              payment_status: 'paid',
              status: 'completed',
            })
            .eq('id', paymentIntent.metadata.task_id);
        }

        // Generate an invoice for the payment
        if (payment) {
          try {
            // Call the invoice creation endpoint
            const response = await fetch(`${process.env.API_URL || 'http://localhost:3001'}/api/stripe-invoice/create-invoice`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ paymentId: payment.id }),
            });

            if (!response.ok) {
              const errorData = await response.json();
              console.error('Error creating invoice:', errorData);
            } else {
              const invoice = await response.json();
              console.log(`Created invoice ${invoice.id} for payment ${payment.id}`);

              // Send an email notification about the invoice
              // This would typically call your email service
              console.log(`Invoice email would be sent to customer for invoice ${invoice.id}`);
            }
          } catch (invoiceError) {
            console.error('Error generating invoice:', invoiceError);
          }
        }

        break;
      }

      case 'payment_intent.payment_failed': {
        const paymentIntent = event.data.object;

        console.log(`Payment intent ${paymentIntent.id} failed`);

        // Update the payment record
        await supabase
          .from('payments')
          .update({
            status: 'failed',
            updated_at: new Date().toISOString(),
          })
          .eq('payment_intent_id', paymentIntent.id);

        break;
      }

      case 'account.application.deauthorized': {
        const account = event.data.object;

        console.log(`Account ${account.id} deauthorized`);

        // Update our database to mark the account as deauthorized
        await supabase
          .from('stripe_accounts')
          .update({
            account_status: 'deauthorized',
            updated_at: new Date().toISOString(),
          })
          .eq('account_id', account.id);

        break;
      }

      case 'capability.updated': {
        const capability = event.data.object;

        console.log(`Capability ${capability.id} updated for account ${capability.account}`);

        // Get the current account status
        const { data: account, error } = await supabase
          .from('stripe_accounts')
          .select('*')
          .eq('account_id', capability.account)
          .single();

        if (error) {
          console.error('Error fetching account:', error);
          break;
        }

        // Fetch the full account from Stripe to get the latest status
        const stripeAccount = await stripe.accounts.retrieve(capability.account);

        // Update our database with the latest account status
        await supabase
          .from('stripe_accounts')
          .update({
            charges_enabled: stripeAccount.charges_enabled,
            payouts_enabled: stripeAccount.payouts_enabled,
            account_status: stripeAccount.charges_enabled && stripeAccount.payouts_enabled ? 'active' : 'pending',
            updated_at: new Date().toISOString(),
          })
          .eq('account_id', capability.account);

        break;
      }

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    res.json({ received: true });
  } catch (err) {
    console.error(`Error processing webhook: ${err.message}`);
    res.status(500).send(`Error processing webhook: ${err.message}`);
  }
});

export default router;