// Follow this setup guide to integrate the Deno runtime into your application:
// https://deno.land/manual/examples/deploy_node_server

// Import required dependencies
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { SmtpClient } from "https://deno.land/x/smtp@v0.7.0/mod.ts";

interface EmailConfig {
  provider: string;
  fromEmail: string;
  fromName?: string;

  // SendGrid specific
  apiKey?: string;
  sendgridTemplateId?: string;

  // Mailgun specific
  mailgunDomain?: string;

  // SMTP specific
  smtpHost?: string;
  smtpPort?: number;
  smtpUsername?: string;
  smtpPassword?: string;
  smtpSecure?: boolean;
}

interface EmailParams {
  to: string;
  subject: string;
  body: string;
  templateId?: string;
  variables?: Record<string, string>;
}

interface EmailRequest {
  config: EmailConfig;
  params: EmailParams;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, {
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
      },
    });
  }

  // Only allow POST requests
  if (req.method !== "POST") {
    return new Response(JSON.stringify({ error: "Method not allowed" }), {
      status: 405,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
    });
  }

  try {
    // Parse the request body
    const requestData: EmailRequest = await req.json();
    const { config, params } = requestData;

    // Validate the request
    if (!config || !params) {
      return new Response(JSON.stringify({ error: "Missing required parameters" }), {
        status: 400,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
        },
      });
    }

    // Validate email parameters
    if (!params.to || !params.subject) {
      return new Response(
        JSON.stringify({
          error: "Missing email parameters. Please provide to and subject.",
        }),
        {
          status: 400,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
          },
        }
      );
    }

    // Get the Resend API key from environment variables
    const RESEND_API_KEY = Deno.env.get("RESEND_API_KEY");

    if (!RESEND_API_KEY) {
      return new Response(
        JSON.stringify({ error: "RESEND_API_KEY environment variable is not set" }),
        {
          status: 500,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
          }
        }
      );
    }

    // Debug mode - just log the email and return success
    if (config.provider === "none") {
      console.log("Debug mode - would have sent email:", {
        to: params.to,
        subject: params.subject,
        body: params.body ? params.body.substring(0, 100) + "..." : "(empty)"
      });

      return new Response(
        JSON.stringify({
          success: true,
          message: `Email would be sent to ${params.to} (DEBUG MODE)`,
        }),
        {
          status: 200,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
          },
        }
      );
    }

    // Log the email sending request
    console.log("Sending email with configuration:", {
      provider: "resend", // Always use Resend regardless of the provider in config
      from: `${config.fromName || "Classtasker"} <<EMAIL>>`,
      to: params.to,
      subject: params.subject,
    });

    // Create plain text version by stripping HTML tags
    const plainText = params.body.replace(/<[^>]*>/g, '');

    // Use Resend API to send the email
    try {
      console.log("Sending email via Resend API...");

      // Prepare the request to Resend API
      const data = {
        from: `${config.fromName || "Classtasker"} <<EMAIL>>`,
        to: [params.to],
        subject: params.subject,
        html: params.body,
        text: plainText
      };

      // Send the request to Resend API
      const response = await fetch("https://api.resend.com/emails", {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${RESEND_API_KEY}`,
          "Content-Type": "application/json"
        },
        body: JSON.stringify(data)
      });

      // Log the response status
      console.log(`Resend API response status: ${response.status}`);

      // Get the response text
      const responseText = await response.text();
      console.log(`Resend API response: ${responseText}`);

      // Check if the request was successful
      if (!response.ok) {
        throw new Error(`Resend API error: ${response.status} - ${responseText}`);
      }

      // Parse the response JSON
      let responseData;
      try {
        responseData = JSON.parse(responseText);
      } catch (e) {
        console.error("Failed to parse Resend API response as JSON:", e);
        responseData = { id: "unknown" };
      }

      // Return success response
      return new Response(
        JSON.stringify({
          success: true,
          message: `Email sent to ${params.to} using Resend API. ID: ${responseData.id || "unknown"}`,
          data: responseData
        }),
        {
          status: 200,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
          },
        }
      );
    } catch (error) {
      console.error("Error sending email via Resend API:", error);
      throw error;
    }
  } catch (error) {
    console.error("Error sending email:", error);

    // Return error response
    return new Response(
      JSON.stringify({
        success: false,
        message: `Error sending email: ${error.message}`,
      }),
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
        },
      }
    );
  }
});
