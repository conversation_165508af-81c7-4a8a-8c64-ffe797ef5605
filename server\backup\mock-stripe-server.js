import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const app = express();
const PORT = 3001;

// Enable CORS for all routes
app.use(cors({
  origin: ['http://localhost:8084', 'http://localhost:8083', 'http://localhost:8082', 'http://localhost:8081', 'http://localhost:8080'],
  credentials: true
}));

// Parse JSON request body
app.use(express.json());

// Create a Stripe Connect Express account
app.post('/api/stripe-connect/create-account', (req, res) => {
  console.log('Mock server: Creating Express account');

  const { userId } = req.body;

  if (!userId) {
    return res.status(400).json({ error: 'Missing userId parameter' });
  }

  // Create a mock account
  const mockAccountId = `acct_test_${Math.random().toString(36).substring(2, 15)}`;

  // Return a mock response
  res.json({
    account: {
      id: mockAccountId,
      user_id: userId,
      account_id: mockAccountId,
      account_type: 'express',
      charges_enabled: false,
      payouts_enabled: false,
      account_status: 'pending',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  });
});

// Generate an onboarding link for a Stripe Connect Express account
app.post('/api/stripe-connect/onboarding-link', (req, res) => {
  console.log('Mock server: Generating onboarding link');

  const { accountId } = req.body;

  if (!accountId) {
    return res.status(400).json({ error: 'Missing accountId parameter' });
  }

  // Return a mock response
  res.json({
    url: `https://connect.stripe.com/express/mock-onboarding/${accountId}`
  });
});

// Generate a login link for the Stripe Connect Express dashboard
app.post('/api/stripe-connect/dashboard-link', (req, res) => {
  console.log('Mock server: Generating dashboard link');

  const { accountId } = req.body;

  if (!accountId) {
    return res.status(400).json({ error: 'Missing accountId parameter' });
  }

  // Return a mock response
  res.json({
    url: `https://connect.stripe.com/express/mock-dashboard/${accountId}`
  });
});

// Get the status of a Stripe Connect Express account
app.get('/api/stripe-connect/account-status/:accountId', (req, res) => {
  console.log('Mock server: Getting account status');

  const { accountId } = req.params;

  if (!accountId) {
    return res.status(400).json({ error: 'Missing accountId parameter' });
  }

  // Return a mock response
  res.json({
    id: accountId,
    charges_enabled: false,
    payouts_enabled: false,
    requirements: {
      currently_due: ['external_account'],
      eventually_due: ['external_account'],
      pending_verification: [],
    },
    capabilities: {
      card_payments: { requested: true, status: 'pending' },
      transfers: { requested: true, status: 'pending' },
    },
  });
});

// Simple route for testing
app.get('/', (req, res) => {
  res.json({ status: 'Mock Stripe Connect API server is running' });
});

// Start the server
app.listen(PORT, () => {
  console.log(`Mock Stripe Connect API server listening on port ${PORT}`);
});