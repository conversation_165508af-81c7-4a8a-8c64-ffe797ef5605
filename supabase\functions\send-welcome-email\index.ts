import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// Resend configuration - using same setup as other email functions
const RESEND_API_KEY = Deno.env.get('RESEND_API_KEY') || ''
const RESEND_FROM_EMAIL = '<EMAIL>'
const RESEND_FROM_NAME = 'Classtasker Support'

interface WelcomeEmailRequest {
  email: string
  user_type: 'school' | 'supplier'
  user_name?: string
  organization_name?: string
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Parse the request body
    const { email, user_type, user_name, organization_name }: WelcomeEmailRequest = await req.json()

    // Validate required fields
    if (!email || !user_type) {
      return new Response(
        JSON.stringify({
          error: 'Missing required fields: email, user_type'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    // Validate user_type
    if (user_type !== 'school' && user_type !== 'supplier') {
      return new Response(
        JSON.stringify({
          error: 'user_type must be either "school" or "supplier"'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    // Validate email address
    if (!email.includes('@') || email.trim() === '') {
      return new Response(
        JSON.stringify({
          error: 'Invalid email address'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    // Check if Resend API key is set
    if (!RESEND_API_KEY) {
      console.error('RESEND_API_KEY environment variable is not set')
      return new Response(
        JSON.stringify({
          error: 'Email service not configured'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500,
        }
      )
    }

    console.log(`Sending welcome email to ${email} for ${user_type} using Resend`)

    // Generate the email content based on user type
    const { subject, emailContent } = generateWelcomeEmailContent(user_type, user_name, organization_name)

    // Send the email using Resend API
    const emailSuccess = await sendEmail(email, subject, emailContent)

    if (emailSuccess) {
      console.log(`Successfully sent welcome email to ${email}`)
      return new Response(
        JSON.stringify({
          success: true,
          message: 'Welcome email sent successfully'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        }
      )
    } else {
      throw new Error('Failed to send welcome email')
    }

  } catch (error) {
    console.error('Error sending welcome email:', error)
    return new Response(
      JSON.stringify({
        error: error.message || 'Failed to send welcome email'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})

function generateWelcomeEmailContent(userType: 'school' | 'supplier', userName?: string, organizationName?: string): { subject: string, emailContent: string } {
  if (userType === 'school') {
    return {
      subject: "Welcome to Classtasker - Your School Management Journey Begins!",
      emailContent: generateSchoolWelcomeEmail(userName, organizationName)
    }
  } else {
    return {
      subject: "Welcome to Classtasker - Connect with Schools and Grow Your Business!",
      emailContent: generateSupplierWelcomeEmail(userName, organizationName)
    }
  }
}

function generateSchoolWelcomeEmail(userName?: string, organizationName?: string): string {
  const currentYear = new Date().getFullYear()
  const greeting = userName ? `Hello ${userName}` : 'Hello'
  const orgText = organizationName ? ` at ${organizationName}` : ''

  return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Classtasker</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background-color: #4F46E5; color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0;">
        <h1 style="margin: 0; font-size: 28px; font-weight: bold;">Welcome to Classtasker!</h1>
        <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Your School Management Journey Begins</p>
    </div>

    <div style="background-color: #f8f9fa; padding: 40px 30px; border-radius: 0 0 8px 8px; border: 1px solid #e9ecef;">
        <h2 style="color: #4F46E5; margin-top: 0; font-size: 24px;">${greeting}${orgText}!</h2>

        <p style="font-size: 16px; margin-bottom: 20px;">Congratulations on successfully setting up your Classtasker account! You're now ready to streamline your school's task management and connect with trusted suppliers.</p>

        <div style="background-color: #e8f4fd; padding: 20px; border-radius: 6px; margin: 25px 0;">
            <h3 style="color: #4F46E5; margin-top: 0; font-size: 18px;">🎯 What You Can Do Now:</h3>
            <ul style="margin: 10px 0; padding-left: 20px;">
                <li style="margin-bottom: 8px;"><strong>Create Tasks:</strong> Set up internal, compliance, and external tasks</li>
                <li style="margin-bottom: 8px;"><strong>Invite Team Members:</strong> Add your staff and assign roles</li>
                <li style="margin-bottom: 8px;"><strong>Manage Compliance:</strong> Stay on top of regulatory requirements</li>
                <li style="margin-bottom: 8px;"><strong>Connect with Suppliers:</strong> Find and work with trusted service providers</li>
            </ul>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <a href="https://classtasker.com/dashboard" style="background-color: #4F46E5; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold; font-size: 16px; margin-right: 10px;">Go to Dashboard</a>
            <a href="https://classtasker.com/help" style="background-color: #6B7280; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold; font-size: 16px;">Get Help</a>
        </div>

        <hr style="border: none; border-top: 1px solid #e9ecef; margin: 30px 0;">

        <p style="font-size: 14px; color: #666; margin-bottom: 5px;"><strong>Need Support?</strong></p>
        <p style="font-size: 14px; color: #666;">Our team is here to help you get the most out of Classtasker. Contact us at <a href="mailto:<EMAIL>" style="color: #4F46E5;"><EMAIL></a> if you have any questions.</p>

        <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #e9ecef;">
            <p style="font-size: 12px; color: #999; margin: 0;">© ${currentYear} Classtasker. All rights reserved.</p>
            <p style="font-size: 12px; color: #999; margin: 5px 0 0 0;">Making school task management simple and efficient.</p>
        </div>
    </div>
</body>
</html>`
}

function generateSupplierWelcomeEmail(userName?: string, organizationName?: string): string {
  const currentYear = new Date().getFullYear()
  const greeting = userName ? `Hello ${userName}` : 'Hello'
  const orgText = organizationName ? ` from ${organizationName}` : ''

  return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Classtasker</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background-color: #059669; color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0;">
        <h1 style="margin: 0; font-size: 28px; font-weight: bold;">Welcome to Classtasker!</h1>
        <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Connect with Schools & Grow Your Business</p>
    </div>

    <div style="background-color: #f8f9fa; padding: 40px 30px; border-radius: 0 0 8px 8px; border: 1px solid #e9ecef;">
        <h2 style="color: #059669; margin-top: 0; font-size: 24px;">${greeting}${orgText}!</h2>

        <p style="font-size: 16px; margin-bottom: 20px;">Welcome to the Classtasker supplier network! You're now connected to a growing community of schools looking for trusted service providers like you.</p>

        <div style="background-color: #ecfdf5; padding: 20px; border-radius: 6px; margin: 25px 0;">
            <h3 style="color: #059669; margin-top: 0; font-size: 18px;">🚀 What You Can Do Now:</h3>
            <ul style="margin: 10px 0; padding-left: 20px;">
                <li style="margin-bottom: 8px;"><strong>Browse Opportunities:</strong> Find tasks and projects from schools</li>
                <li style="margin-bottom: 8px;"><strong>Submit Proposals:</strong> Respond to requests with your offers</li>
                <li style="margin-bottom: 8px;"><strong>Build Relationships:</strong> Connect directly with school staff</li>
                <li style="margin-bottom: 8px;"><strong>Grow Your Business:</strong> Expand your client base in education</li>
            </ul>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <a href="https://classtasker.com/marketplace" style="background-color: #059669; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold; font-size: 16px; margin-right: 10px;">Browse Marketplace</a>
            <a href="https://classtasker.com/help" style="background-color: #6B7280; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold; font-size: 16px;">Get Help</a>
        </div>

        <div style="background-color: #fef3c7; padding: 20px; border-radius: 6px; margin: 25px 0; border-left: 4px solid #f59e0b;">
            <h3 style="color: #92400e; margin-top: 0; font-size: 16px;">💡 Pro Tip:</h3>
            <p style="color: #92400e; margin: 0; font-size: 14px;">Complete your supplier profile and add your services to increase your visibility to schools. The more detailed your profile, the more likely schools are to choose you!</p>
        </div>

        <hr style="border: none; border-top: 1px solid #e9ecef; margin: 30px 0;">

        <p style="font-size: 14px; color: #666; margin-bottom: 5px;"><strong>Need Support?</strong></p>
        <p style="font-size: 14px; color: #666;">Our team is here to help you succeed on Classtasker. Contact us at <a href="mailto:<EMAIL>" style="color: #059669;"><EMAIL></a> if you have any questions.</p>

        <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #e9ecef;">
            <p style="font-size: 12px; color: #999; margin: 0;">© ${currentYear} Classtasker. All rights reserved.</p>
            <p style="font-size: 12px; color: #999; margin: 5px 0 0 0;">Connecting schools with trusted suppliers.</p>
        </div>
    </div>
</body>
</html>`
}

async function sendEmail(to: string, subject: string, htmlContent: string): Promise<boolean> {
  try {
    // Validate email address
    if (!to || !to.includes('@') || to.trim() === '') {
      console.error(`Invalid email address: "${to}"`)
      return false
    }

    // Clean up the email address
    to = to.trim()

    // Check if Resend API key is set
    if (!RESEND_API_KEY) {
      console.error('RESEND_API_KEY environment variable is not set. Email sending will fail.')
      return false
    }

    console.log(`Sending email to ${to} with subject "${subject}" using Resend`)

    // Create plain text version by stripping HTML tags
    const plainText = htmlContent.replace(/<[^>]*>/g, '')

    // For debugging, log the request details
    console.log('Resend request details:', {
      apiKey: `${RESEND_API_KEY.substring(0, 8)}...`,
      from: `${RESEND_FROM_NAME} <${RESEND_FROM_EMAIL}>`,
      to: to,
      subject: subject
    })

    // Prepare the request to Resend API
    const data = {
      from: `${RESEND_FROM_NAME} <${RESEND_FROM_EMAIL}>`,
      to: [to],
      subject: subject,
      html: htmlContent,
      text: plainText
    }

    // Send the request to Resend API
    const response = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${RESEND_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    })

    // Log the response status
    console.log(`Resend API response status: ${response.status}`)

    // Get the response text
    const responseText = await response.text()
    console.log(`Resend API response: ${responseText}`)

    // Check if the request was successful
    if (!response.ok) {
      throw new Error(`Resend API error: ${response.status} - ${responseText}`)
    }

    // Try to parse the response as JSON
    let result
    try {
      result = JSON.parse(responseText)
      console.log(`Email sent successfully to ${to} using Resend. Message ID: ${result.id}`)
    } catch (parseError) {
      console.log(`Email sent successfully to ${to} using Resend, but couldn't parse response: ${responseText}`)
    }

    return true
  } catch (error) {
    console.error('Error sending email:', error)
    return false
  }
}
