import { useAuth } from '@/contexts/AuthContext';

export type Permission =
  | 'manage_site'           // Site-wide management
  | 'manage_organizations'  // Manage organizations
  | 'manage_users'          // Manage users
  | 'manage_tasks'          // Manage tasks
  | 'manage_payments'       // Manage payments and billing
  | 'create_tasks'          // Create tasks
  | 'handle_tasks'          // Handle assigned tasks
  | 'view_public_tasks'     // View public tasks
  | 'submit_offers';        // Submit offers for tasks

/**
 * Hook for checking role-based permissions
 * @returns Object with permission checking functions
 */
export const useRolePermissions = () => {
  const { user, profile, userRole } = useAuth();

  // Check if user is a site admin
  const isSiteAdmin = !!profile?.is_site_admin;

  // Check if user has a specific permission
  const hasPermission = (permission: Permission): boolean => {
    // Not authenticated
    if (!user || !profile) return false;

    // Site admins have all permissions
    if (isSiteAdmin) return true;

    // Role-based permissions
    switch (permission) {
      case 'manage_site':
        return isSiteAdmin;

      case 'manage_organizations':
        return userRole === 'admin';

      case 'manage_users':
        return userRole === 'admin';

      case 'manage_tasks':
        return userRole === 'admin';

      case 'manage_payments':
        return isSiteAdmin || userRole === 'admin';

      case 'create_tasks':
        return userRole === 'admin' || userRole === 'teacher';

      case 'handle_tasks':
        return userRole === 'admin' || userRole === 'maintenance' || userRole === 'support';

      case 'view_public_tasks':
        return true; // All authenticated users

      case 'submit_offers':
        return profile.account_type === 'supplier';

      default:
        return false;
    }
  };

  // Get all permissions for the current user
  const getAllPermissions = (): Permission[] => {
    const allPermissions: Permission[] = [
      'manage_site',
      'manage_organizations',
      'manage_users',
      'manage_tasks',
      'manage_payments',
      'create_tasks',
      'handle_tasks',
      'view_public_tasks',
      'submit_offers'
    ];

    return allPermissions.filter(permission => hasPermission(permission));
  };

  return {
    isSiteAdmin,
    hasPermission,
    getAllPermissions
  };
};

export default useRolePermissions;
