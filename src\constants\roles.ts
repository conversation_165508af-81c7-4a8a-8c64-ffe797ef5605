/**
 * Role Management System for ClassTasker
 * 
 * This file defines the role constants, groups, and helper functions for the application.
 * It provides a centralized place for role-related functionality.
 */

/**
 * Role constants for the application
 */
export const ROLES = {
  // External roles
  SUPPLIER: 'supplier',

  // Organization roles
  ADMIN: 'admin',

  // Internal staff roles
  MAINTENANCE: 'maintenance',
  SUPPORT: 'support',
  CLEANER: 'cleaner',
  IT: 'it',
  TEACHER: 'teacher',
  FINANCE: 'finance',
  RECEPTION: 'reception'
} as const;

/**
 * Type for role values
 */
export type Role = typeof ROLES[keyof typeof ROLES];

/**
 * Role groups for common access patterns
 */
export const ROLE_GROUPS = {
  /**
   * All school staff roles
   */
  SCHOOL_STAFF: [
    ROLES.ADMIN,
    ROLES.MAINTENANCE,
    ROLES.SUPPORT,
    ROLES.CLEANER,
    ROLES.IT,
    ROLES.TEACHER,
    ROLES.FINANCE,
    ROLES.RECEPTION
  ],
  
  /**
   * Roles that can be assigned tasks
   */
  TASK_ASSIGNABLE: [
    ROLES.MAINTENANCE,
    ROLES.SUPPORT,
    ROLES.CLEANER,
    ROLES.IT
  ],
  
  /**
   * Roles that can create tasks
   */
  TASK_CREATORS: [
    ROLES.ADMIN,
    ROLES.TEACHER,
    ROLES.RECEPTION
  ]
} as const;

/**
 * Helper function to check if a role is in a role group
 */
export function isInRoleGroup(role: string, group: readonly string[]): boolean {
  return group.includes(role);
}

/**
 * Helper function to check if a role can be assigned tasks
 */
export function isAssignable(role: string): boolean {
  return isInRoleGroup(role, ROLE_GROUPS.TASK_ASSIGNABLE);
}

/**
 * Helper function to check if a role can create tasks
 */
export function isTaskCreator(role: string): boolean {
  return isInRoleGroup(role, ROLE_GROUPS.TASK_CREATORS);
}

/**
 * Helper function to check if a role is a school staff role
 */
export function isSchoolStaff(role: string): boolean {
  return isInRoleGroup(role, ROLE_GROUPS.SCHOOL_STAFF);
}

/**
 * Helper function to get the display name for a role
 */
export function getDisplayNameForRole(role: string): string {
  switch (role) {
    case ROLES.ADMIN: return 'Administrator';
    case ROLES.MAINTENANCE: return 'Maintenance Staff';
    case ROLES.SUPPORT: return 'Support Worker';
    case ROLES.CLEANER: return 'Cleaning Staff';
    case ROLES.IT: return 'IT Support';
    case ROLES.TEACHER: return 'Teacher';
    case ROLES.FINANCE: return 'Finance Staff';
    case ROLES.RECEPTION: return 'Reception Staff';
    case ROLES.SUPPLIER: return 'External Supplier';
    default: return role.charAt(0).toUpperCase() + role.slice(1);
  }
}
